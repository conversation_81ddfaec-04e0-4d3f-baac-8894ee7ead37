import { request } from '@/utils/request';

// 数据整编与分类相关API接口

/**
 * 创建分类任务
 * @param data 任务数据
 */
export function createClassificationTask(data: FormData) {
  return request({
    url: '/classification_api/create_task',
    method: 'POST',
    data,
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });
}

/**
 * 获取任务列表
 * @param params 查询参数
 */
export function getTaskList(params: { page: number; page_size: number }) {
  return request({
    url: '/classification_api/get_task_list',
    method: 'POST',
    data: params,
    headers: {
      'Content-Type': 'application/json;charset=utf-8',
    },
  });
}

/**
 * 下载模板文件
 */
export function downloadTemplate() {
  return request({
    url: '/classification_api/download_template',
    method: 'GET',
    responseType: 'blob',
  });
}

/**
 * 获取任务详情
 * @param taskId 任务ID
 */
export function getTaskDetail(taskId: string) {
  return request({
    url: `/classification_api/task_detail/${taskId}`,
    method: 'GET',
  });
}

/**
 * 删除任务
 * @param taskId 任务ID
 */
export function deleteTask(taskId: string) {
  return request({
    url: `/classification_api/delete_task/${taskId}`,
    method: 'DELETE',
  });
}

/**
 * 获取分类结果
 * @param taskId 任务ID
 */
export function getClassificationResult(taskId: string) {
  return request({
    url: `/classification_api/get_result/${taskId}`,
    method: 'GET',
  });
}

/**
 * 导出分类结果
 * @param taskId 任务ID
 * @param format 导出格式 (excel/txt)
 */
export function exportClassificationResult(taskId: string, format: 'excel' | 'txt') {
  return request({
    url: `/classification_api/export_result/${taskId}`,
    method: 'GET',
    params: { format },
    responseType: 'blob',
  });
}

/**
 * 批量导出
 * @param taskIds 任务ID列表
 * @param format 导出格式
 */
export function batchExport(taskIds: string[], format: 'excel' | 'txt') {
  return request({
    url: '/classification_api/batch_export',
    method: 'POST',
    data: { task_ids: taskIds, format },
    responseType: 'blob',
  });
}

/**
 * 获取元数据字段配置
 */
export function getMetadataFields() {
  return request({
    url: '/classification_api/get_metadata_fields',
    method: 'GET',
  });
}

/**
 * 更新任务状态
 * @param taskId 任务ID
 * @param status 状态
 */
export function updateTaskStatus(taskId: string, status: string) {
  return request({
    url: `/classification_api/update_task_status/${taskId}`,
    method: 'PUT',
    data: { status },
  });
}

/**
 * 重新运行分类任务
 * @param taskId 任务ID
 */
export function rerunClassificationTask(taskId: string) {
  return request({
    url: `/classification_api/rerun_task/${taskId}`,
    method: 'POST',
  });
}

/**
 * 获取分类统计信息
 * @param taskId 任务ID
 */
export function getClassificationStats(taskId: string) {
  return request({
    url: `/classification_api/get_stats/${taskId}`,
    method: 'GET',
  });
}
