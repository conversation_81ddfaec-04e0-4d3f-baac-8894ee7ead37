<script setup lang="ts">
import {
  HeartIcon,
  ShareIcon,
  ViewListIcon,
  ViewModuleIcon,
  CalculatorIcon,
  ChevronLeftIcon
} from 'tdesign-icons-vue-next';
import {
  Button,
  Checkbox,
  CheckboxGroup,
  Empty,
  Loading,
  Pagination,
  Select,
  Table,
  Tag,
  Dialog
} from 'tdesign-vue-next';
import { computed, h, onMounted, reactive, ref } from 'vue';

import {
  getPhaseChangeInfo,
  getEnthalpyOfVaporizationInfo,
  getEntropyOfVaporizationInfo,
  getEnthalpyParametersInfo,
  getAntoineParametersInfo,
  getEnthalpyOfFusionInfo,
  getEntropyFusionInfo,
  getEnthalpySublimationInfo,
  getEntropySublimationInfo,
  getEnthalpyTransitionInfo,
  getEntropyTransitionInfo,
  getReducedPressureBoilingPointInfo,
  getTemperatureOfPhaseTransitionInfo
} from '../api';
import { useSearchStore } from '#/store/search';

interface PhaseChangeInfo {
  quantity: string;
  value: string;
  units: string;
  method: string;
  reference: string;
  comment: string;
}

interface EnthalpyInfo {
  cpgas: string;
  temperature: string;
  reference: string;
  comment: string;
}

interface EntropyInfo {
  vaps: string;
  temperature: string;
  reference: string;
  comment: string;
}

interface AntoineInfo {
  formula: string;
  temperature: string;
  a: string;
  b: string;
  c: string;
  reference: string;
  comment: string;
}

interface EnthalpyFusionInfo {
  fush: string;
  temperature: string;
  reference: string;
  comment: string;
}

interface EntropyFusionInfo {
  fuss: string;
  temperature: string;
  reference: string;
  comment: string;
}

interface EnthalpySublimationInfo {
  subh: string;
  temperature: string;
  method: string;
  reference: string;
  comment: string;
}

interface EntropySublimationInfo {
  subs: string;
  temperature: string;
  reference: string;
  comment: string;
}

interface EnthalpyTransitionInfo {
  htrs: string;
  temperature: string;
  initialPhase: string;
  finalPhase: string;
  reference: string;
  comment: string;
}

interface EntropyTransitionInfo {
  strs: string;
  temperature: string;
  initialPhase: string;
  finalPhase: string;
  reference: string;
  comment: string;
}

interface ReducedInfo {
  tboil: string;
  pressure: string;
  reference: string;
  comment: string;
}

interface TemperatureInfo {
  trs: string;
  initialPhase: string;
  finalPhase: string;
  reference: string;
  comment: string;
}

/**
 * 内部静态数据定义
 */
const state: any = reactive({
  /**
   * 当前详情数据
   */
  detailItem: {},
  /**
  * 加载状态
  */
  loading: true,

});
// 响应式数据
const phaseList = ref<PhaseChangeInfo[][]>([]);
const enthalpyList = ref<EnthalpyInfo[]>([]);
const entropyList = ref<EntropyInfo[]>([]);
const enthalpyParameters = ref<any>([]);
const enthalpyFormula = ref('');
const antoineList = ref<AntoineInfo[]>([]);
const enthalpyFusionList = ref<EnthalpyFusionInfo[]>([]);
const entropyFusionList = ref<EntropyFusionInfo[][]>([]);
const enthalpySublimationList = ref<EnthalpySublimationInfo[]>([]);
const entropySublimationList = ref<EntropySublimationInfo[]>([]);
const enthalpyTransitionList = ref<EnthalpyTransitionInfo[]>([]);
const entropyTransitionList = ref<EntropyTransitionInfo[]>([]);
const reducedList = ref<ReducedInfo[]>([]);
const temperatureList = ref<TemperatureInfo[]>([]);

// 表格列定义
const getPhaseChangeColumns = () => [
  {
    colKey: 'quantity',
    title: 'Quantity',
    width: 150,
    ellipsis: true,
    cell: (h, { row }) => h('span', { innerHTML: row.quantity })
  },
  {
    colKey: 'value',
    title: 'Value',
    width: 100,
    ellipsis: true,
    cell: (h, { row }) => h('span', { innerHTML: row.value })
  },
  {
    colKey: 'units',
    title: 'Units',
    width: 80,
    ellipsis: true,
    cell: (h, { row }) => h('span', { innerHTML: row.units })
  },
  {
    colKey: 'method',
    title: 'Method',
    width: 80,
    ellipsis: true,
    cell: (h, { row }) => h('span', { innerHTML: row.method })
  },
  {
    colKey: 'reference',
    title: 'Reference',
    width: 150,
    ellipsis: true,
    cell: (h, { row }) => h('span', { innerHTML: row.reference })
  },
  {
    colKey: 'comment',
    title: 'Comment',
    width: 400,
    ellipsis: true,
    cell: (h, { row }) => h('span', { innerHTML: row.comment })
  },
];

const getEnthalpyColumns = () => [
  {
    colKey: 'id',
    title: 'ID',
  },
  {
    colKey: 'vaph',
    title: (h) => h('div', [
      h('div', ['Δ', h('sub', 'vap'),
        'H (kJ/mol)'])
    ]),
    width: 150,
    ellipsis: true,
    cell: (h, { row }) => h('span', { innerHTML: row.vaph })
  },
  {
    colKey: 'temperature',
    title: (h) => h('div', [
      h('div', 'Temperature(K)')
    ]),
    width: 150,
    ellipsis: true,
    cell: (h, { row }) => h('span', { innerHTML: row.temperature })
  },
  {
    colKey: 'method',
    title: 'Method',
    width: 80,
    ellipsis: true,
    cell: (h, { row }) => h('span', { innerHTML: row.method })
  },
  {
    colKey: 'reference',
    title: 'Reference',
    width: 300,
    ellipsis: true,
    cell: (h, { row }) => h('span', { innerHTML: row.reference })
  },
  {
    colKey: 'comment',
    title: 'Comment',
    width: 500,
    ellipsis: true,
    cell: (h, { row }) => h('span', { innerHTML: row.comment })
  }
];

const getEntropyColumns = () => [
  {
    colKey: 'vaps',
    title: (h) => h('div', [
      h('div', ['Δ', h('sub', 'vap'),
        'S (kJ/mol)'])
    ]),
    width: 150,
    ellipsis: true,
    cell: (h, { row }) => h('span', { innerHTML: row.vaps })
  },
  {
    colKey: 'temperature',
    title: (h) => h('div', [
      h('div', 'Temperature(K)')
    ]),
    width: 150,
    ellipsis: true,
    cell: (h, { row }) => h('span', { innerHTML: row.temperature })
  },
  {
    colKey: 'reference',
    title: 'Reference',
    width: 300,
    ellipsis: true,
    cell: (h, { row }) => h('span', { innerHTML: row.reference })
  },
  {
    colKey: 'comment',
    title: 'Comment',
    width: 500,
    ellipsis: true,
    cell: (h, { row }) => h('span', { innerHTML: row.comment })
  }
];

const getAntoineColumns = () => [
  {
    colKey: 'id',
    title: 'ID',
  },
  {
    colKey: 'temperature',
    title: 'Temperature(K)',
    width: 150,
    ellipsis: true,
    cell: (h, { row }) => h('span', { innerHTML: row.temperature })
  },
  {
    colKey: 'a',
    title: 'A',
    width: 80,
    ellipsis: true,
    cell: (h, { row }) => h('span', { innerHTML: row.a })
  },
  {
    colKey: 'b',
    title: 'B',
    width: 80,
    ellipsis: true,
    cell: (h, { row }) => h('span', { innerHTML: row.b })
  },
  {
    colKey: 'c',
    title: 'C',
    width: 80,
    ellipsis: true,
    cell: (h, { row }) => h('span', { innerHTML: row.c })
  },
  {
    colKey: 'reference',
    title: 'Reference',
    width: 300,
    ellipsis: true,
    cell: (h, { row }) => h('span', { innerHTML: row.reference })
  },
  {
    colKey: 'comment',
    title: 'Comment',
    width: 500,
    ellipsis: true,
    cell: (h, { row }) => h('span', { innerHTML: row.comment })
  }
];

const getFusionColumns = () => [
  {
    colKey: 'id',
    title: 'ID',
  },
  {
    colKey: 'fush',
    title: (h) => h('div', [
      h('div', ['Δ', h('sub', 'fus'),
        'H (kJ/mol)'])
    ]),
    width: 150,
    ellipsis: true,
    cell: (h, { row }) => h('span', { innerHTML: row.fush })
  },
  {
    colKey: 'temperature',
    title: 'Temperature(K)',
    width: 150,
    ellipsis: true,
    cell: (h, { row }) => h('span', { innerHTML: row.temperature })
  },
  {
    colKey: 'reference',
    title: 'Reference',
    width: 300,
    ellipsis: true,
    cell: (h, { row }) => h('span', { innerHTML: row.reference })
  },
  {
    colKey: 'comment',
    title: 'Comment',
    width: 500,
    ellipsis: true,
    cell: (h, { row }) => h('span', { innerHTML: row.comment })
  }
];

const getEntropyFusionColumns = () => [
  {
    colKey: 'fuss',
    title: (h) => h('div', [
      h('div', ['Δ', h('sub', 'fus'),
        'S (kJ/mol)'])
    ]),
    width: 150,
    ellipsis: true,
    cell: (h, { row }) => h('span', { innerHTML: row.fuss })
  },
  {
    colKey: 'temperature',
    title: (h) => h('div', [
      h('div', 'Temperature(K)')
    ]),
    width: 150,
    ellipsis: true,
    cell: (h, { row }) => h('span', { innerHTML: row.temperature })
  },
  {
    colKey: 'reference',
    title: 'Reference',
    width: 300,
    ellipsis: true,
    cell: (h, { row }) => h('span', { innerHTML: row.reference })
  },
  {
    colKey: 'comment',
    title: 'Comment',
    width: 500,
    ellipsis: true,
    cell: (h, { row }) => h('span', { innerHTML: row.comment })
  }
];

const getEnthalpySublimationColumns = () => [
  {
    colKey: 'subh',
    title: (h) => h('div', [
      h('div', ['Δ', h('sub', 'sub'),
        'H (kJ/mol)'])
    ]),
    width: 150,
    ellipsis: true,
    cell: (h, { row }) => h('span', { innerHTML: row.subh })
  },
  {
    colKey: 'temperature',
    title: (h) => h('div', [
      h('div', 'Temperature(K)')
    ]),
    width: 150,
    ellipsis: true,
    cell: (h, { row }) => h('span', { innerHTML: row.temperature })
  },
  {
    colKey: 'method',
    title: 'Method',
    width: 150,
    ellipsis: true,
    cell: (h, { row }) => h('span', { innerHTML: row.method })
  },
  {
    colKey: 'reference',
    title: 'Reference',
    width: 300,
    ellipsis: true,
    cell: (h, { row }) => h('span', { innerHTML: row.reference })
  },
  {
    colKey: 'comment',
    title: 'Comment',
    width: 200,
    ellipsis: true,
    cell: (h, { row }) => h('span', { innerHTML: row.comment })
  }
];

const getEntropySublimationColumns = () => [
  {
    colKey: 'subs',
    title: (h) => h('div', [
      h('div', ['Δ', h('sub', 'sub'),
        'S (kJ/mol)'])
    ]),
    width: 150,
    ellipsis: true,
    cell: (h, { row }) => h('span', { innerHTML: row.subs })
  },
  {
    colKey: 'temperature',
    title: (h) => h('div', [
      h('div', 'Temperature(K)')
    ]),
    width: 150,
    ellipsis: true,
    cell: (h, { row }) => h('span', { innerHTML: row.temperature })
  },
  {
    colKey: 'reference',
    title: 'Reference',
    width: 300,
    ellipsis: true,
    cell: (h, { row }) => h('span', { innerHTML: row.reference })
  },
  {
    colKey: 'comment',
    title: 'Comment',
    width: 200,
    ellipsis: true,
    cell: (h, { row }) => h('span', { innerHTML: row.comment })
  }
];

const getEnthalpyTransitionColumns = () => [
  {
    colKey: 'htrs',
    title: (h) => h('div', [
      h('div', ['ΔH', h('sub', 'trs'),
        ' (kJ/mol)'])
    ]),
    width: 150,
    ellipsis: true,
    cell: (h, { row }) => h('span', { innerHTML: row.htrs })
  },
  {
    colKey: 'temperature',
    title: (h) => h('div', [
      h('div', 'Temperature(K)')
    ]),
    width: 150,
    ellipsis: true,
    cell: (h, { row }) => h('span', { innerHTML: row.temperature })
  },
  {
    colKey: 'initialPhase',
    title: 'Initial Phase',
    width: 150,
    ellipsis: true,
    cell: (h, { row }) => h('span', { innerHTML: row.initialPhase })
  },
  {
    colKey: 'finalPhase',
    title: 'Final Phase',
    width: 150,
    ellipsis: true,
    cell: (h, { row }) => h('span', { innerHTML: row.finalPhase })
  },
  {
    colKey: 'reference',
    title: 'Reference',
    width: 300,
    ellipsis: true,
    cell: (h, { row }) => h('span', { innerHTML: row.reference })
  },
  {
    colKey: 'comment',
    title: 'Comment',
    width: 200,
    ellipsis: true,
    cell: (h, { row }) => h('span', { innerHTML: row.comment })
  }
];

const getEntropyTransitionColumns = () => [
  {
    colKey: 'strs',
    title: (h) => h('div', [
      h('div', ['ΔS', h('sub', 'trs'),
        ' (kJ/mol)'])
    ]),
    width: 150,
    ellipsis: true,
    cell: (h, { row }) => h('span', { innerHTML: row.strs })
  },
  {
    colKey: 'temperature',
    title: (h) => h('div', [
      h('div', 'Temperature(K)')
    ]),
    width: 150,
    ellipsis: true,
    cell: (h, { row }) => h('span', { innerHTML: row.temperature })
  },
  {
    colKey: 'initialPhase',
    title: 'Initial Phase',
    width: 150,
    ellipsis: true,
    cell: (h, { row }) => h('span', { innerHTML: row.initialPhase })
  },
  {
    colKey: 'finalPhase',
    title: 'Final Phase',
    width: 150,
    ellipsis: true,
    cell: (h, { row }) => h('span', { innerHTML: row.finalPhase })
  },
  {
    colKey: 'reference',
    title: 'Reference',
    width: 300,
    ellipsis: true,
    cell: (h, { row }) => h('span', { innerHTML: row.reference })
  },
  {
    colKey: 'comment',
    title: 'Comment',
    width: 200,
    ellipsis: true,
    cell: (h, { row }) => h('span', { innerHTML: row.comment })
  }
];

const getReducedColumns = () => [
  {
    colKey: 'tboil',
    title: (h) => h('div', [
      h('div', ['T', h('sub', 'boil'),
        ' (K)'])
    ]),
    width: 150,
    ellipsis: true,
    cell: (h, { row }) => h('span', { innerHTML: row.tboil })
  },
  {
    colKey: 'pressure',
    title: (h) => h('div', [
      h('div', 'Pressure(bar)')
    ]),
    width: 150,
    ellipsis: true,
    cell: (h, { row }) => h('span', { innerHTML: row.pressure })
  },
  {
    colKey: 'reference',
    title: 'Reference',
    width: 300,
    ellipsis: true,
    cell: (h, { row }) => h('span', { innerHTML: row.reference })
  },
  {
    colKey: 'comment',
    title: 'Comment',
    width: 200,
    ellipsis: true,
    cell: (h, { row }) => h('span', { innerHTML: row.comment })
  }
];

const getTemperatureColumns = () => [
  {
    colKey: 'trs',
    title: (h) => h('div', [
      h('div', ['T', h('sub', 'trs'),
        ' (K)'])
    ]),
    width: 150,
    ellipsis: true,
    cell: (h, { row }) => h('span', { innerHTML: row.trs })
  },
  {
    colKey: 'initialPhase',
    title: 'Initial Phase',
    width: 150,
    ellipsis: true,
    cell: (h, { row }) => h('span', { innerHTML: row.initialPhase })
  },
  {
    colKey: 'finalPhase',
    title: 'Final Phase',
    width: 150,
    ellipsis: true,
    cell: (h, { row }) => h('span', { innerHTML: row.finalPhase })
  },
  {
    colKey: 'reference',
    title: 'Reference',
    width: 300,
    ellipsis: true,
    cell: (h, { row }) => h('span', { innerHTML: row.reference })
  },
  {
    colKey: 'comment',
    title: 'Comment',
    width: 200,
    ellipsis: true,
    cell: (h, { row }) => h('span', { innerHTML: row.comment })
  }
];

// Store
const searchStore = useSearchStore();
// 获取当前详情数据
state.detailItem = computed(() => {
  // 获取当前详情数据
  const detail = searchStore.currentDetailItem;
  // 如果当前详情数据存在，则返回当前详情数据
  if (detail) {
    // 返回当前详情数据
    return detail;
  } else {
    // 如果当前详情数据不存在，则从本地存储中获取当前详情数据
    const local = localStorage.getItem('currentDetailItem');
    // 如果本地存储中不存在当前详情数据，则返回空对象
    return JSON.parse(local ?? '{}');
  }
});

const displayColumns = ref(['vaph', 'temperature', 'method', 'reference', 'comment']);
const displayColumnsAntoine = ref(['temperature', 'a', 'b', 'c', 'reference', 'comment']);
const displayColumnsFusion = ref(['fush', 'temperature', 'reference', 'comment']);

const getEntropyFusionRowspanAndColspan = ({ col, rowIndex }, entropy) => {
  const references = entropy.map(item => item.reference);
  const allSame = new Set(references).size === 1;
  if (col.colKey === 'reference') {
    if (allSame) {
      if (rowIndex === 0) {
        return { rowspan: entropy.length, colspan: 1 };
      } else {
        return { rowspan: 0, colspan: 0 };
      }
    } else {
      return { rowspan: 1, colspan: 1 };
    }
  } else if (col.colKey === 'comment') {
    if (allSame) {
      if (rowIndex === 0) {
        return { rowspan: entropy.length, colspan: 1 };
      } else {
        return { rowspan: 0, colspan: 0 };
      }
    } else {
      return { rowspan: 1, colspan: 1 };
    }
  }
  return { rowspan: 1, colspan: 1 };
}

onMounted(async () => {
  try {
    // 调用相变信息查询API
    const response = await getPhaseChangeInfo({ iupacStandardInchiKey: state.detailItem.baseCode });
    phaseList.value = response;
    // 调用相变汽化焓信息查询API
    const res = await getEnthalpyOfVaporizationInfo({ iupacStandardInchiKey: state.detailItem.baseCode });
    enthalpyList.value = res;
    // 调用相变汽化焓信息查询API
    const resVaporization = await getEntropyOfVaporizationInfo({ iupacStandardInchiKey: state.detailItem.baseCode });
    entropyList.value = resVaporization;
    // 调用相变汽化焓信息查询API
    const resData = await getEnthalpyParametersInfo({ iupacStandardInchiKey: state.detailItem.baseCode });
    enthalpyParameters.value = formattedEnthalpyParameters(resData);
    enthalpyFormula.value = resData.formula;
    // 调用获取相变安托万方程参数信息查询API
    const resArr = await getAntoineParametersInfo({ iupacStandardInchiKey: state.detailItem.baseCode });
    antoineList.value = resArr;
    // 调用获取相变熔化焓信息
    const resArray = await getEnthalpyOfFusionInfo({ iupacStandardInchiKey: state.detailItem.baseCode });
    enthalpyFusionList.value = resArray;
    // 调用获取相变融合熵信息
    const resEntropy = await getEntropyFusionInfo({ iupacStandardInchiKey: state.detailItem.baseCode });
    entropyFusionList.value = resEntropy;
    // 调用获取相变升华焓信息
    const resSublimation = await getEnthalpySublimationInfo({ iupacStandardInchiKey: state.detailItem.baseCode });
    enthalpySublimationList.value = resSublimation;
    // 调用获取相变升华熵信息
    const resSub = await getEntropySublimationInfo({ iupacStandardInchiKey: state.detailItem.baseCode });
    entropySublimationList.value = resSub;
    // 调用获取相变焓信息
    const resTransition = await getEnthalpyTransitionInfo({ iupacStandardInchiKey: state.detailItem.baseCode });
    enthalpyTransitionList.value = resTransition;
    // 调用获取相变熵信息
    const resTran = await getEntropyTransitionInfo({ iupacStandardInchiKey: state.detailItem.baseCode });
    entropyTransitionList.value = resTran;
    // 调用获取相变减压沸点信息
    const resReduced = await getReducedPressureBoilingPointInfo({ iupacStandardInchiKey: state.detailItem.baseCode });
    reducedList.value = resReduced;
    // 调用获取相变温度信息
    const resTemperature = await getTemperatureOfPhaseTransitionInfo({ iupacStandardInchiKey: state.detailItem.baseCode });
    temperatureList.value = resTemperature;
  } catch (error) {
    console.error('获取相变信息失败:', error);
  } finally {
    // 关闭加载状态
    state.loading = false;
  }
});

function formattedEnthalpyParameters(data: any) {
  return [
    { param: 'Temperature (K)', value: data.temperature },
    { param: 'A (kJ/mol)', value: data.a },
    { param: 'α', value: data.alpha },
    { param: 'β', value: data.beta },
    { param: 'Tc (K)', value: data.tc },
    { param: 'Reference', value: data.reference }
  ];
}
</script>

<template>
  <div class="phase-info">
    <div v-if="phaseList.length > 0">
      <h2>Phase change data</h2>
      <div v-for="phase in phaseList">
        <Table :data="phase" :columns="getPhaseChangeColumns()" style="border:1px solid #e6e8eb" :bordered="true"
          resizable :hover="true" :stripe="true" row-key="quantity" table-layout="fixed" cell-empty-content="-" />
      </div>
      <br />
    </div>
    <div v-if="enthalpyList.length > 0">
      <h2>Enthalpy of vaporization</h2>
      <Table :data="enthalpyList" :columns="getEnthalpyColumns()" :displayColumns="displayColumns"
        style="border:1px solid #e6e8eb" :bordered="true" resizable :hover="true" :stripe="true" row-key="id"
        table-layout="fixed" cell-empty-content="-" />
      <br />
    </div>
    <div v-if="enthalpyParameters.length > 0">
      <h2>Enthalpy of vaporization</h2>
      <div class="formula" v-if="enthalpyFormula">
        <div v-html="enthalpyFormula"></div>
      </div>
      <div class="enthalpy-parameters">
        <div class="row" v-for="(item, index) in enthalpyParameters" :key="index">
          <div class="param" v-if="item.param !== 'Tc (K)'">
            {{ item.param }}
          </div>
          <div class="param" v-else>
            T<sub>c</sub>(K)
          </div>
          <div class="value">
            {{ item.value }}
          </div>
        </div>
      </div>
      <br />
    </div>
    <div v-if="entropyList.length > 0">
      <h2>Entropy of vaporization</h2>
      <Table :data="entropyList" :columns="getEntropyColumns()" style="border:1px solid #e6e8eb" :bordered="true"
        resizable :hover="true" :stripe="true" row-key="id" table-layout="fixed" cell-empty-content="-" />
      <br />
    </div>
    <div v-if="antoineList.length > 0">
      <h2>Antoine Equation Parameters</h2>
      <div class="formula" v-if="antoineList[0]">
        <div v-html="antoineList[0].formula"></div>
      </div>
      <Table :data="antoineList" :columns="getAntoineColumns()" :displayColumns="displayColumnsAntoine" :bordered="true"
        resizable :hover="true" :stripe="true" row-key="id" table-layout="fixed" cell-empty-content="-"
        style="border:1px solid #e6e8eb;margin-top: 10px;" />
      <br />
    </div>
    <div v-if="enthalpyFusionList.length > 0">
      <h2>Enthalpy of fusion</h2>
      <Table :data="enthalpyFusionList" :columns="getFusionColumns()" :displayColumns="displayColumnsFusion"
        style="border:1px solid #e6e8eb" :bordered="true" resizable :hover="true" :stripe="true" row-key="id"
        table-layout="fixed" cell-empty-content="-" />
      <br />
    </div>
    <div v-if="entropyFusionList.length > 0">
      <h2>Entropy of fusion</h2>
      <div v-for="entropy in entropyFusionList">
        <Table :data="entropy" :columns="getEntropyFusionColumns()"
          :rowspan-and-colspan="raw => getEntropyFusionRowspanAndColspan(raw, entropy)" style="border:1px solid #e6e8eb"
          :bordered="true" resizable :hover="true" :stripe="true" row-key="id" table-layout="fixed"
          cell-empty-content="-" />
      </div>
      <br />
    </div>
    <div v-if="enthalpySublimationList.length > 0">
      <h2>Enthalpy of sublimation</h2>
      <Table :data="enthalpySublimationList" :columns="getEnthalpySublimationColumns()" style="border:1px solid #e6e8eb"
        :bordered="true" resizable :hover="true" :stripe="true" row-key="id" table-layout="fixed"
        cell-empty-content="-" />
      <br />
    </div>
    <div v-if="entropySublimationList.length > 0">
      <h2>Entropy of sublimation</h2>
      <Table :data="entropySublimationList" :columns="getEntropySublimationColumns()" style="border:1px solid #e6e8eb"
        :bordered="true" resizable :hover="true" :stripe="true" row-key="id" table-layout="fixed"
        cell-empty-content="-" />
      <br />
    </div>
    <div v-if="enthalpyTransitionList.length > 0">
      <h2>Enthalpy of phase transition</h2>
      <Table :data="enthalpyTransitionList" :columns="getEnthalpyTransitionColumns()" style="border:1px solid #e6e8eb"
        :bordered="true" resizable :hover="true" :stripe="true" row-key="id" table-layout="fixed"
        cell-empty-content="-" />
      <br />
    </div>
    <div v-if="entropyTransitionList.length > 0">
      <h2>Entropy of phase transition</h2>
      <Table :data="entropyTransitionList" :columns="getEntropyTransitionColumns()" style="border:1px solid #e6e8eb"
        :bordered="true" resizable :hover="true" :stripe="true" row-key="id" table-layout="fixed"
        cell-empty-content="-" />
      <br />
    </div>
    <div v-if="reducedList.length > 0">
      <h2>Reduced pressure boiling point</h2>
      <Table :data="reducedList" :columns="getReducedColumns()" style="border:1px solid #e6e8eb" :bordered="true"
        resizable :hover="true" :stripe="true" row-key="id" table-layout="fixed" cell-empty-content="-" />
      <br />
    </div>
    <div v-if="temperatureList.length > 0">
      <h2>Temperature of phase transition</h2>
      <Table :data="temperatureList" :columns="getTemperatureColumns()" style="border:1px solid #e6e8eb"
        :bordered="true" resizable :hover="true" :stripe="true" row-key="id" table-layout="fixed"
        cell-empty-content="-" />
      <br />
    </div>
  </div>
</template>

<style scoped lang="scss">
.phase-info {
  h2 {
    font-size: 20px;
    margin-bottom: 10px;
  }

  .formula {
    line-height: 1.6;
    font-size: 14px;
    margin-bottom: 10px;
    padding: 10px;
  }

  .enthalpy-parameters {
    margin-top: 10px;
    display: table;
    width: 50%;
    border-collapse: collapse;

    .row {
      display: table-row;

      .param,
      .value {
        display: table-cell;
        padding: 8px;
        border: 1px solid #ddd;
        text-align: center;
      }

      .param {
        font-size: 14px;
        color: #fff !important;
        background-color: rgb(0, 57, 138);
        font-weight: bold;
        text-align: left;
      }

      .value {
        background-color: #fff;
        text-align: right;
      }
    }
  }

  :deep(.t-table td),
  :deep(.t-table th) {
    border-right: 1px solid #e6e8eb !important;
  }

  :deep(.t-table td:last-child),
  :deep(.t-table th:last-child) {
    border-right: none !important;
  }

  :deep(.t-table td) {
    max-height: 2.8em !important;
    line-height: 1.4 !important;
    padding: 8px !important;
    overflow: hidden !important;
    word-break: break-word !important;
  }
}
</style>
