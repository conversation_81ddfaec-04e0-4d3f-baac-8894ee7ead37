<script setup lang="ts">
import {
  HeartIcon,
  ShareIcon,
  ViewListIcon,
  ViewModuleIcon,
  CalculatorIcon,
  ChevronLeftIcon
} from 'tdesign-icons-vue-next';
import {
  Button,
  Checkbox,
  CheckboxGroup,
  Empty,
  Loading,
  Pagination,
  Select,
  Table,
  Tag,
  Dialog
} from 'tdesign-vue-next';
import { computed, h, onMounted, reactive, ref } from 'vue';

import {
  getCondensedPhaseInfo,
  getLiquidInfo
} from '../api';
import { useSearchStore } from '#/store/search';

interface CondensedPhaseInfo {
  quantity: string;
  value: string;
  units: string;
  method: string;
  reference: string;
  comment: string;
}

interface LiquidInfo {
  liquid: string;
  temperature: string;
  reference: string;
  comment: string;
}
/**
 * 内部静态数据定义
 */
const state: any = reactive({
  /**
   * 当前详情数据
   */
  detailItem: {},
  /**
  * 加载状态
  */
  loading: true,

});
// 响应式数据
const condensedList = ref<CondensedPhaseInfo[][]>([]);
const liquidList = ref<LiquidInfo[]>([]);
// 表格列定义
const getCondensedColumns = () => [
  {
    colKey: 'quantity',
    title: 'Quantity',
    width: 150,
    ellipsis: true,
    cell: (h, { row }) => h('span', { innerHTML: row.quantity })
  },
  {
    colKey: 'value',
    title: 'Value',
    width: 100,
    ellipsis: true,
    cell: (h, { row }) => h('span', { innerHTML: row.value })
  },
  {
    colKey: 'units',
    title: 'Units',
    width: 80,
    ellipsis: true,
    cell: (h, { row }) => h('span', { innerHTML: row.units })
  },
  {
    colKey: 'method',
    title: 'Method',
    width: 80,
    ellipsis: true,
    cell: (h, { row }) => h('span', { innerHTML: row.method })
  },
  {
    colKey: 'reference',
    title: 'Reference',
    width: 150,
    ellipsis: true,
    cell: (h, { row }) => h('span', { innerHTML: row.reference })
  },
  {
    colKey: 'comment',
    title: 'Comment',
    width: 400,
    ellipsis: true,
    cell: (h, { row }) => h('span', { innerHTML: row.comment })
  },
];

const getLiquidColumns = () => [
  {
    colKey: 'id',
    title: 'ID',
  },
  {
    colKey: 'cpgas',
    title: (h) => h('div', {
      style: 'display: grid; gap: 2px;'
    }, [
      h('div', ['C', h('sub', 'p,liquid')]),
      h('div', '(J/mol·K)')
    ]),
    width: 150,
    ellipsis: true,
    cell: (h, { row }) => h('span', { innerHTML: row.cpgas })
  },
  {
    colKey: 'temperature',
    title: (h) => h('div', {
      style: 'display: grid; gap: 2px;'
    }, [
      h('div', 'Temperature'),
      h('div', '(K)')
    ]),
    width: 150,
    ellipsis: true,
    cell: (h, { row }) => h('span', { innerHTML: row.temperature })
  },
  {
    colKey: 'reference',
    title: 'Reference',
    width: 300,
    ellipsis: true,
    cell: (h, { row }) => h('span', { innerHTML: row.reference })
  },
  {
    colKey: 'comment',
    title: 'Comment',
    width: 500,
    ellipsis: true,
    cell: (h, { row }) => h('span', { innerHTML: row.comment })
  }
];

// Store
const searchStore = useSearchStore();
// 获取当前详情数据
state.detailItem = computed(() => {
  // 获取当前详情数据
  const detail = searchStore.currentDetailItem;
  // 如果当前详情数据存在，则返回当前详情数据
  if (detail) {
    // 返回当前详情数据
    return detail;
  } else {
    // 如果当前详情数据不存在，则从本地存储中获取当前详情数据
    const local = localStorage.getItem('currentDetailItem');
    // 如果本地存储中不存在当前详情数据，则返回空对象
    return JSON.parse(local ?? '{}');
  }
});

const displayColumns = ref(['cpgas', 'temperature', 'reference', 'comment']);
onMounted(async () => {
  try {
    // 调用凝聚相信息查询API
    const response = await getCondensedPhaseInfo({ iupacStandardInchiKey: state.detailItem.baseCode });
    condensedList.value = response;
    // 调用凝聚相相变液体恒压热容信息查询API
    const res = await getLiquidInfo({ iupacStandardInchiKey: state.detailItem.baseCode });
    liquidList.value = res;
  } catch (error) {
    console.error('获取凝聚相信息失败:', error);
  } finally {
    // 关闭加载状态
    state.loading = false;
  }
});
</script>

<template>
  <div class="condensed-phase-info">
    <div v-if="condensedList && condensedList.length > 0">
      <h2>Condensed phase thermochemistry data</h2>
      <div v-for="condensed in condensedList">
        <Table :data="condensed" :columns="getCondensedColumns()" style="border:1px solid #e6e8eb" :bordered="true"
          resizable :hover="true" :stripe="true" row-key="quantity" table-layout="fixed" cell-empty-content="-" />
      </div>
    </div>
    <div v-if="liquidList && liquidList.length > 0">
      <br />
      <h2>Constant pressure heat capacity of liquid</h2>
      <Table :data="liquidList" :columns="getLiquidColumns()" :displayColumns="displayColumns"
        style="border:1px solid #e6e8eb" :bordered="true" resizable :hover="true" :stripe="true" row-key="id"
        table-layout="fixed" cell-empty-content="-" />
    </div>
  </div>
</template>

<style scoped lang="scss">
.condensed-phase-info {
  h2 {
    font-size: 20px;
    margin-bottom: 10px;
  }

  :deep(.t-table td),
  :deep(.t-table th) {
    border-right: 1px solid #e6e8eb !important;
  }

  :deep(.t-table td:last-child),
  :deep(.t-table th:last-child) {
    border-right: none !important;
  }

  :deep(.t-table td) {
    max-height: 2.8em !important;
    line-height: 1.4 !important;
    padding: 8px !important;
    overflow: hidden !important;
    word-break: break-word !important;
  }
}
</style>
