import { request } from '@/utils/request';

// 质量控制相关API接口

/**
 * 获取上传列表
 */
export function getUploadList() {
  return request({
    url: '/api/upload_list',
    method: 'GET',
  });
}

/**
 * 开始质量控制
 * @param data 质量控制参数
 */
export function startQualityControl(data: any) {
  return request({
    url: '/api/quality_control/start',
    method: 'POST',
    data,
  });
}

/**
 * 获取质量控制结果
 * @param taskId 任务ID
 */
export function getQualityControlResult(taskId: string) {
  return request({
    url: `/api/quality_control/result/${taskId}`,
    method: 'GET',
  });
}

/**
 * 上传Excel文件
 * @param file 文件
 */
export function uploadExcelFile(file: FormData) {
  return request({
    url: '/api/quality_control/upload_excel',
    method: 'POST',
    data: file,
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });
}

/**
 * 获取数据库列表
 */
export function getDatabaseList() {
  return request({
    url: '/api/quality_control/databases',
    method: 'GET',
  });
}

/**
 * 获取数据库表列表
 * @param database 数据库名称
 */
export function getTableList(database: string) {
  return request({
    url: `/api/quality_control/tables/${database}`,
    method: 'GET',
  });
}

/**
 * 获取字段列表
 * @param database 数据库名称
 * @param table 表名称
 */
export function getFieldList(database: string, table: string) {
  return request({
    url: `/api/quality_control/fields/${database}/${table}`,
    method: 'GET',
  });
}

/**
 * 下载质量控制报告
 * @param taskId 任务ID
 */
export function downloadQualityReport(taskId: string) {
  return request({
    url: `/api/quality_control/download/${taskId}`,
    method: 'GET',
    responseType: 'blob',
  });
}
