<script setup lang="ts">
import {
  HeartIcon,
  ShareIcon,
  ViewListIcon,
  ViewModuleIcon,
  CalculatorIcon,
  ChevronLeftIcon
} from 'tdesign-icons-vue-next';
import {
  Button,
  Checkbox,
  CheckboxGroup,
  Empty,
  Loading,
  Pagination,
  Select,
  Table,
  Tag,
  Dialog
} from 'tdesign-vue-next';
import { computed, h, onMounted, reactive, ref } from 'vue';

import {
  getHlConstantWaterSolutionInfo
} from '../api';
import { useSearchStore } from '#/store/search';

interface HlConstantWaterSolutionInfo {
  formula: string;
  kh: string;
  d: string;
  method: string;
  reference: string;
  comment: string;
}

/**
 * 内部静态数据定义
 */
const state: any = reactive({
  /**
   * 当前详情数据
   */
  detailItem: {},
  /**
  * 加载状态
  */
  loading: true,

});
// 响应式数据
const hlConstantList = ref<HlConstantWaterSolutionInfo[]>([]);

// 表格列定义
const getHlConstantWaterSolutionColumns = () => [
  {
    colKey: 'id',
    title: 'ID',
  },
  {
    colKey: 'kh',
    title: (h) => h('div', [
      h('div', ['k°', h('sub', 'H'),
        '(mol/(kg*bar))'])
    ]),
    width: 150,
    ellipsis: true,
    cell: (h, { row }) => h('span', { innerHTML: row.kh })
  },
  {
    colKey: 'd',
    title: (h) => h('div', [
      h('div', ['d(ln(k', h('sub', 'H'),
        '))/d(1/T) (K)'])
    ]),
    width: 150,
    ellipsis: true,
    cell: (h, { row }) => h('span', { innerHTML: row.d })
  },
  {
    colKey: 'method',
    title: 'Method',
    width: 80,
    ellipsis: true,
    cell: (h, { row }) => h('span', { innerHTML: row.method })
  },
  {
    colKey: 'reference',
    title: 'Reference',
    width: 300,
    ellipsis: true,
    cell: (h, { row }) => h('span', { innerHTML: row.reference })
  },
  {
    colKey: 'comment',
    title: 'Comment',
    width: 500,
    ellipsis: true,
    cell: (h, { row }) => h('span', { innerHTML: row.comment })
  }
];

// Store
const searchStore = useSearchStore();
// 获取当前详情数据
state.detailItem = computed(() => {
  // 获取当前详情数据
  const detail = searchStore.currentDetailItem;
  // 如果当前详情数据存在，则返回当前详情数据
  if (detail) {
    // 返回当前详情数据
    return detail;
  } else {
    // 如果当前详情数据不存在，则从本地存储中获取当前详情数据
    const local = localStorage.getItem('currentDetailItem');
    // 如果本地存储中不存在当前详情数据，则返回空对象
    return JSON.parse(local ?? '{}');
  }
});

const displayColumns = ref(['kh', 'd', 'method', 'reference', 'comment']);

onMounted(async () => {
  try {
    // 调用相变信息查询API
    const response = await getHlConstantWaterSolutionInfo({ iupacStandardInchiKey: state.detailItem.baseCode });
    hlConstantList.value = response;
  } catch (error) {
    console.error('获取亨利定律信息失败:', error);
  } finally {
    // 关闭加载状态
    state.loading = false;
  }
});
</script>

<template>
  <div class="hlConstant-info">
    <div v-if="hlConstantList && hlConstantList.length > 0">
      <h1>Henry's Law data</h1>
      <h2>Henry's Law constant (water solution)</h2>
      <div class="formula" v-if="hlConstantList[0]">
        <div v-html="hlConstantList[0].formula"></div>
      </div>
      <Table :data="hlConstantList" :columns="getHlConstantWaterSolutionColumns()" :displayColumns="displayColumns"
        :bordered="true" :hover="true" :stripe="true" row-key="id" table-layout="fixed" cell-empty-content="-"
        style="margin-top: 10px;" />
    </div>
  </div>
</template>

<style scoped lang="scss">
.hlConstant-info {
  h1 {
    font-size: 24px;
    margin-bottom: 10px;
  }

  h2 {
    font-size: 20px;
    margin-bottom: 10px;
  }

  .formula {
    line-height: 1.6;
    font-size: 14px;
    margin-bottom: 10px;
  }

  :deep(.t-table td),
  :deep(.t-table th) {
    border-right: 1px solid #e6e8eb !important;
  }

  :deep(.t-table td:last-child),
  :deep(.t-table th:last-child) {
    border-right: none !important;
  }

  :deep(.t-table td)  {
    max-height: 2.8em !important;
    line-height: 1.4 !important;
    padding: 8px !important;
    overflow: hidden !important;
    word-break: break-word !important;
  }
}
</style>
