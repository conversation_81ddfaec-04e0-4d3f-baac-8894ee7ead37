<script setup lang="ts">
import {
  <PERSON><PERSON>,
  <PERSON>,
  Dialog,
  Loading,
  Progress,
  Radio,
  RadioGroup,
  Select,
  Table,
  TabPanel,
  Tabs,
} from 'tdesign-vue-next';
import { ref } from 'vue';

// 响应式数据
const loading = ref(false);
const activeName = ref('first');
const radio1 = ref('1');
const iscenter = ref(1);
const dialogVisible = ref(false);
const selectedRow = ref({});
const selectedDatabase = ref('');
const selectedTable = ref('');
const requiredField = ref('');
const taskName = ref('');

// 表格数据
const tableData = ref([
  {
    publicationType: '',
    authors: '<PERSON><PERSON>, <PERSON>; <PERSON>, <PERSON>aO7; <PERSON><PERSON>, <PERSON>',
    articleTitle: 'Data quality for dat INTERNATIONAL',
    volume: '154',
    issue: '',
    specialIssue: '',
    startPage: '72',
    endPage: '90',
  },
  {
    publicationType: '',
    authors: '<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>; <PERSON>, <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>',
    articleTitle: 'Deep learning and MECHANICAL SY',
    volume: '115',
    issue: '',
    specialIssue: '',
    startPage: '213',
    endPage: '237',
  },
  {
    publicationType: '',
    authors: 'Ming, Weng, Zhou',
    articleTitle: 'COVID-19 and Air EMERGING MAR',
    volume: '56',
    issue: '10',
    specialIssue: 'SI',
    startPage: '2422',
    endPage: '2442',
  },
  {
    publicationType: '',
    authors: 'Tokogron, C. J., A; Tian, Qiuyun',
    articleTitle: 'Structural Health IEEE INTERNET (C',
    volume: '4',
    issue: '3',
    specialIssue: '',
    startPage: '619',
    endPage: '636',
  },
  {
    publicationType: '',
    authors: 'Douzdot, Jason; H; He, Data',
    articleTitle: 'Using Deep Learn IEEE TRANSACT)',
    volume: '48',
    issue: '11',
    specialIssue: '',
    startPage: '111',
    endPage: '20',
  },
]);

const tableData1 = ref([
  {
    index: 1,
    field: 'Title',
    trueCount: 1229,
    falseCount: 25,
    truePercentage: 85.5,
    falsePercentage: 14.5,
  },
  {
    index: 2,
    field: 'Author',
    trueCount: 1189,
    falseCount: 65,
    truePercentage: 85.5,
    falsePercentage: 14.5,
  },
  {
    index: 3,
    field: 'Institute',
    trueCount: 833,
    falseCount: 421,
    truePercentage: 85.5,
    falsePercentage: 14.5,
  },
  {
    index: 4,
    field: 'insertTime',
    trueCount: 1254,
    falseCount: 0,
    truePercentage: 100,
    falsePercentage: 0,
  },
  {
    index: 5,
    field: 'updateTime',
    trueCount: 1254,
    falseCount: 0,
    truePercentage: 100,
    falsePercentage: 0,
  },
]);

const customColors = '#0052d9';

// 表格列配置
const columns = [
  { colKey: 'publicationType', title: 'Publication Type', width: 180 },
  { colKey: 'authors', title: 'Authors', width: 300, ellipsis: true },
  { colKey: 'articleTitle', title: 'Article Title', width: 300 },
  { colKey: 'volume', title: 'Volume', width: 100 },
  { colKey: 'issue', title: 'Issue', width: 100 },
  { colKey: 'specialIssue', title: 'Special Issue', width: 120 },
  { colKey: 'startPage', title: 'Start Page', width: 100 },
  { colKey: 'endPage', title: 'End Page', width: 100 },
];

const columns1 = [
  { colKey: 'index', title: '序号', width: 60 },
  { colKey: 'field', title: '字段名' },
  { colKey: 'trueCount', title: '有值数量' },
  { colKey: 'falseCount', title: '空值数量' },
  {
    colKey: 'truePercentage',
    title: '有值/空值比',
    cell: (h: any, { row }: any) => {
      return h('div', { style: 'display: flex; align-items: center;' }, [
        h(Progress, {
          percentage: row.truePercentage,
          color: customColors,
          showText: false,
          strokeWidth: 12,
          style: 'width: 100px; margin-right: 10px;',
        }),
        h('span', { innerHTML: row.falsePercentage }),
      ]);
    },
  },
];

// 方法
const handleClick = (value: string) => {
  activeName.value = value;
  switch (value) {
    case 'fourth': {
      iscenter.value = 5;

      break;
    }
    case 'second': {
      iscenter.value = 1;

      break;
    }
    case 'third': {
      iscenter.value = 4;

      break;
    }
    default: {
      iscenter.value = 1;
    }
  }
};

const handleClickspan = (row: any) => {
  selectedRow.value = row;
  dialogVisible.value = true;
};

const statrZK = () => {
  if (activeName.value === 'second') {
    iscenter.value = 3;
  } else if (activeName.value === 'fourth') {
    iscenter.value = 5;
  } else {
    iscenter.value = 2;
  }
};

// 数据库选项
const databaseOptions = [{ label: '**********', value: '**********' }];

const fieldOptions = [
  { label: 'id', value: 'id' },
  { label: 'time', value: 'time' },
];
</script>

<template>
  <div class="quality-control-container">
    <Card class="main-card">
      <template #header>
        <div class="card-header">
          <h2 class="page-title">质量控制工具</h2>
        </div>
      </template>

      <div class="description">
        <p class="description-text">
          质量控制工具包括完整性质控工具，确保元数据无缺失、无冗余；规范性质控工具，确保格式、内容符合标准；唯一性质控工具，确保数据保持唯一，符合既定标准；及时性质控工具，确保数据持续更新、不过期。
        </p>
      </div>

      <Loading :loading="loading" show-overlay prevent-scroll-through>
        <div class="content-area">
          <div class="tabs-header">
            <Tabs v-model="activeName" @change="handleClick">
              <TabPanel value="first" label="完整性" />
              <TabPanel value="second" label="规范性" />
              <TabPanel value="third" label="唯一性" />
              <TabPanel value="fourth" label="合规性" />
            </Tabs>
            <Button
              theme="success"
              @click="dialogVisible = true"
              class="task-list-btn"
            >
              任务列表
            </Button>
          </div>

          <!-- 完整性/规范性/合规性 - 输入表单 -->
          <div v-if="iscenter === 1" class="center">
            <div class="form-section">
              <div class="form-row">
                <span class="form-label">导入形式：</span>
                <RadioGroup v-model="radio1">
                  <Radio value="1" variant="outline">Excel</Radio>
                  <Radio value="2" variant="outline">MySql数据库</Radio>
                </RadioGroup>
              </div>

              <div class="form-row">
                <Button
                  theme="primary"
                  :disabled="radio1 === '2'"
                  class="upload-btn"
                >
                  点击上传
                </Button>

                <span class="form-label">选择数据库：</span>
                <Select
                  v-model="selectedDatabase"
                  placeholder="请选择模型存储地址(例如：123.123.123.123)"
                  :options="databaseOptions"
                  :disabled="radio1 === '1'"
                  class="select-input"
                />

                <span class="form-label">选择数据库表：</span>
                <Select
                  v-model="selectedTable"
                  placeholder="请选择/请先选择数据库"
                  :options="databaseOptions"
                  :disabled="radio1 === '1'"
                  class="select-input"
                />
              </div>
            </div>

            <Table
              :data="tableData"
              :columns="columns"
              bordered
              stripe
              class="data-table"
              :header-style="{
                backgroundColor: '#f6f6f6',
                fontWeight: 'bold',
                color: '#000000',
              }"
            />

            <div class="form-section">
              <div class="form-row">
                <span class="form-label">必备字段：</span>
                <Select
                  v-model="requiredField"
                  placeholder="请选择字段"
                  :options="fieldOptions"
                  class="select-input"
                />
              </div>

              <div class="form-row">
                <span class="form-label">任务名称：</span>
                <Select
                  v-model="taskName"
                  placeholder="请选择字段"
                  :options="fieldOptions"
                  class="select-input"
                />
              </div>
            </div>
          </div>

          <!-- 完整性结果 -->
          <div v-if="iscenter === 2" class="center">
            <div class="stats-section">
              <p><span>总条目数：<em>12554</em>条</span></p>
              <p>
                <span>字段填充率：<em>65%</em></span>
              </p>
              <p><span>空置率<em>35%</em>条</span></p>
            </div>

            <h2 class="section-title">必备字段：</h2>
            <Table
              :data="tableData1"
              :columns="columns1"
              bordered
              stripe
              class="data-table"
              :header-style="{
                backgroundColor: '#f6f6f6',
                fontWeight: 'bold',
                color: '#000000',
              }"
            />
          </div>

          <!-- 规范性结果 -->
          <div v-if="iscenter === 3" class="center">
            <Table
              :data="tableData1"
              :columns="[
                { colKey: 'index', title: '序号', width: 60 },
                { colKey: 'field', title: '字段名' },
                { colKey: 'trueCount', title: '规范数量量' },
                {
                  colKey: 'falseCount',
                  title: '不规范数据量',
                  cell: (h, { row }) =>
                    h(
                      'span',
                      {
                        style:
                          'color: #0052d9; text-decoration: underline; cursor: pointer;',
                        onClick: () => handleClickspan(row),
                      },
                      row.falseCount,
                    ),
                },
                {
                  colKey: 'truePercentage',
                  title: '规范率',
                  cell: (h, { row }) =>
                    h('span', { innerHTML: row.falsePercentage }),
                },
              ]"
              bordered
              stripe
              class="data-table"
              :header-style="{
                backgroundColor: '#f6f6f6',
                fontWeight: 'bold',
                color: '#000000',
              }"
            />
          </div>

          <!-- 唯一性结果 -->
          <div v-if="iscenter === 4" class="center">
            <div class="stats-section">
              <p>
                <span>唯一性：<em>99.76%</em></span>
              </p>
              <p><span>总条目数：<em>12554</em>条</span></p>
              <p>
                <span>字段填充率：<em>65%</em></span>
              </p>
              <p><span>空置率<em>35%</em>条</span></p>
            </div>

            <h2 class="section-title">不唯一条目：</h2>
            <Table
              :data="tableData1"
              :columns="[
                { colKey: 'index', title: '序号', width: 60 },
                { colKey: 'field', title: 'Title' },
                { colKey: 'trueCount', title: 'Author' },
                { colKey: 'falseCount', title: 'Institute' },
                {
                  colKey: 'truePercentage',
                  title: '有值/空值比',
                  cell: (h, { row }) => {
                    return h(
                      'div',
                      { style: 'display: flex; align-items: center;' },
                      [
                        h(Progress, {
                          percentage: row.truePercentage,
                          color: customColors,
                          showText: false,
                          strokeWidth: 12,
                          style: 'width: 100px; margin-right: 10px;',
                        }),
                        h('span', { innerHTML: row.falsePercentage }),
                      ],
                    );
                  },
                },
              ]"
              bordered
              stripe
              class="data-table"
              :header-style="{
                backgroundColor: '#f6f6f6',
                fontWeight: 'bold',
                color: '#000000',
              }"
            />
          </div>

          <!-- 合规性结果 -->
          <div v-if="iscenter === 5" class="center">
            <Table
              :data="tableData1"
              :columns="[
                { colKey: 'index', title: '序号', width: 60 },
                { colKey: 'field', title: '字段名' },
                { colKey: 'trueCount', title: 'Author' },
                { colKey: 'falseCount', title: '不合规类型描述' },
                {
                  colKey: 'truePercentage',
                  title: '有值/空值比',
                  cell: (h, { row }) => {
                    return h(
                      'div',
                      { style: 'display: flex; align-items: center;' },
                      [
                        h(Progress, {
                          percentage: row.truePercentage,
                          color: customColors,
                          showText: false,
                          strokeWidth: 12,
                          style: 'width: 100px; margin-right: 10px;',
                        }),
                        h('span', { innerHTML: row.falsePercentage }),
                      ],
                    );
                  },
                },
              ]"
              bordered
              stripe
              class="data-table"
              :header-style="{
                backgroundColor: '#f6f6f6',
                fontWeight: 'bold',
                color: '#000000',
              }"
            />
          </div>

          <!-- 开始质控按钮 -->
          <div v-if="iscenter !== 4" class="action-section">
            <Button theme="primary" @click="statrZK" class="start-btn">
              开始质控
            </Button>
          </div>
        </div>
      </Loading>
    </Card>

    <!-- 不规范数据详情对话框 -->
    <Dialog v-model:visible="dialogVisible" title="不规范数据详情" width="30%">
      <p>不规范数据量:</p>
      <p>字段名:</p>
      <template #footer>
        <Button @click="dialogVisible = false">关闭</Button>
      </template>
    </Dialog>
  </div>
</template>

<style scoped lang="less">
.quality-control-container {
  padding: 24px;
  background: #f5f7fa;
  min-height: 100vh;
}

.main-card {
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.card-header {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.page-title {
  margin: 0;
  font-size: 24px;
  font-weight: 600;
  color: #333;
}

.description {
  margin-bottom: 32px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 6px;
  border-left: 4px solid #0052d9;
}

.description-text {
  margin: 0;
  line-height: 1.6;
  color: #333;
  font-size: 16px;
}

.content-area {
  min-height: 600px;
}

.tabs-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.task-list-btn {
  margin-left: auto;
}

.center {
  border: 1px solid #ccc;
  width: 100%;
  margin: 20px auto;
  border-radius: 20px;
  padding: 30px;
  min-width: 1200px;
  background: #fff;
}

.form-section {
  margin-bottom: 20px;
}

.form-row {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
  gap: 20px;
}

.form-label {
  font-weight: 500;
  color: #333;
  white-space: nowrap;
}

.select-input {
  width: 300px;
}

.upload-btn {
  margin-right: 74px;
}

.data-table {
  margin: 20px 0;
}

.stats-section {
  margin-bottom: 20px;

  p {
    margin: 10px 0;
    font-size: 16px;

    span {
      color: #76839b;
    }

    em {
      color: #606266;
      font-style: normal;
      font-weight: 600;
    }
  }
}

.section-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin-bottom: 16px;
}

.action-section {
  text-align: right;
  width: 100%;
  margin-top: 20px;
}

.start-btn {
  margin-right: 60px;
}

// 响应式设计
@media (max-width: 1200px) {
  .center {
    min-width: auto;
    padding: 20px;
  }

  .form-row {
    flex-wrap: wrap;
    gap: 10px;
  }

  .select-input {
    width: 250px;
  }
}

@media (max-width: 768px) {
  .quality-control-container {
    padding: 16px;
  }

  .tabs-header {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }

  .form-row {
    flex-direction: column;
    align-items: flex-start;
  }

  .select-input {
    width: 100%;
  }

  .action-section {
    text-align: center;
  }

  .start-btn {
    margin-right: 0;
  }
}
</style>
