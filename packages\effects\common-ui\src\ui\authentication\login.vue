<script setup lang="ts">
import type { Recordable } from '@vben/types';

import type { VbenFormSchema } from '@vben-core/form-ui';

import type { AuthenticationProps } from './types';

import { computed, h, onMounted, reactive, ref } from 'vue';
import { useRouter } from 'vue-router';

import { $t } from '@vben/locales';

import { useVbenForm, z } from '@vben-core/form-ui';
import { VbenButton, VbenCheckbox } from '@vben-core/shadcn-ui';

import Title from './auth-title.vue';
import ThirdPartyLogin from './third-party-login.vue';
import { MessagePlugin } from 'tdesign-vue-next';

// 钉钉登录
import * as dd from 'dingtalk-jsapi';

// 为钉钉登录添加类型声明
declare global {
  interface Window {
    DDLogin?: (config: any) => void;
  }
}

interface Props extends AuthenticationProps {
  formSchema: VbenFormSchema[];
}

defineOptions({
  name: 'AuthenticationLogin',
});

const props = withDefaults(defineProps<Props>(), {
  codeLoginPath: '/auth/code-login',
  forgetPasswordPath: '/auth/forget-password',
  formSchema: () => [],
  loading: false,
  qrCodeLoginPath: '/auth/qrcode-login',
  registerPath: '/auth/register',
  showCodeLogin: true,
  showForgetPassword: false,
  showQrcodeLogin: true,
  showRegister: true,
  showRememberMe: false,
  showThirdPartyLogin: true,
  submitButtonText: '',
  subTitle: '',
  title: '',
});

const emit = defineEmits<{
  submit: [Recordable<any>];
  getCaptcha: [];
  getMobileCaptcha: [];
  sendSmsCode: [string, string, string];
}>();

// 手机号登录相关变量
const isMobileLogin = ref(false);
const smsLoading = ref(false);
const smsCountdown = ref(0);
const smsText = ref('发送验证码');

// 短信验证码倒计时
function startSmsCountdown() {
  smsCountdown.value = 60;
  smsText.value = `${smsCountdown.value}秒后重发`;
  
  const timer = setInterval(() => {
    smsCountdown.value--;
    if (smsCountdown.value > 0) {
      smsText.value = `${smsCountdown.value}秒后重发`;
    } else {
      smsText.value = '重新发送';
      clearInterval(timer);
    }
  }, 1000);
}

// 验证码验证函数
function validateVerifyCode(force = false) {
  // 如果用户还没有开始输入过，且不是强制验证，则不显示错误
  if (!verifyCodeTouched.value && !force) {
    return true;
  }
  
  showVerifyCodeError.value = false;
  verifyCodeError.value = '';
  
  if (!mobileVerifyCode.value) {
    verifyCodeError.value = '请输入验证码';
    showVerifyCodeError.value = true;
    return false;
  }
  
  if (mobileVerifyCode.value.length < 4) {
    verifyCodeError.value = '验证码长度不正确';
    showVerifyCodeError.value = true;
    return false;
  }
  
  return true;
}

// 清除验证码错误
function clearVerifyCodeError() {
  if (showVerifyCodeError.value) {
    showVerifyCodeError.value = false;
    verifyCodeError.value = '';
  }
}

// 处理验证码输入
function handleVerifyCodeInput() {
  verifyCodeTouched.value = true; // 标记用户已经开始输入
  clearVerifyCodeError();
}

// 发送短信验证码函数声明
async function sendSmsCode() {
  try {
    const values = await mobileFormApi.getValues();
    
    if (!values.mobileNumber) {
      // 可以添加提示消息
      return;
    }
    
    // 验证手机号格式
    const phoneRegex = /^1[3-9]\d{9}$/;
    if (!phoneRegex.test(values.mobileNumber)) {
      // 可以添加提示消息
      return;
    }

    smsLoading.value = true;
    
    // 调用发送验证码 - 需要在父组件中通过emit传递authStore或直接调用API
    // 这里发送事件让父组件处理，同时传递图形验证码参数
    emit('sendSmsCode', values.mobileNumber, mobileCaptchaCode.value, mobileCaptchaKey.value);
    
  } catch (error) {
    console.error('发送验证码失败:', error);
    // 发送失败时重置状态
    smsLoading.value = false;
  }
}

// 手机号登录表单schema（只包含手机号，验证码单独处理）
const mobileFormSchema = computed((): VbenFormSchema[] => {
  return [
    {
      component: 'VbenInput',
      componentProps: {
        placeholder: '请输入手机号',
        maxlength: 11,
        type: 'tel',
      },
      fieldName: 'mobileNumber',
      label: '手机号',
      rules: z
        .string()
        .min(1, { message: '请输入手机号' })
        .regex(/^1[3-9]\d{9}$/, { message: '请输入正确的手机号格式' }),
    },
  ];
});

// 添加验证码相关变量
const mobileVerifyCode = ref('');
const verifyCodeError = ref('');
const showVerifyCodeError = ref(false);
const verifyCodeTouched = ref(false); // 追踪用户是否开始输入过

const [Form, formApi] = useVbenForm(
  reactive({
    commonConfig: {
      hideLabel: true,
      hideRequiredMark: true,
    },
    schema: computed(() => props.formSchema),
    showDefaultActions: false,
  }),
);

// 手机号登录表单
const [MobileForm, mobileFormApi] = useVbenForm(
  reactive({
    commonConfig: {
      hideLabel: true,
      hideRequiredMark: true,
    },
    schema: computed(() => mobileFormSchema.value),
    showDefaultActions: false,
  }),
);
const router = useRouter();

const REMEMBER_ME_KEY = `REMEMBER_ME_USERNAME_${location.hostname}`;

const localUsername = localStorage.getItem(REMEMBER_ME_KEY) || '';

const rememberMe = ref(!!localUsername);

// 验证码相关
const captchaCode = ref('');
const captchaImage = ref('');
const captchaKey = ref('');
const captchaLoading = ref(false);
const captchaError = ref('');
const showCaptchaError = ref(false);

// 获取验证码
async function getCaptcha() {
  try {
    // 开始加载
    captchaLoading.value = true;

    // 清空当前输入的验证码和图片
    captchaCode.value = '';
    captchaImage.value = '';

    // 清除验证码错误状态
    clearCaptchaError();

    // 向父组件发送获取验证码的事件
    emit('getCaptcha');

    // 如果5秒后还没有收到验证码，停止loading
    setTimeout(() => {
      if (captchaLoading.value && !captchaImage.value) {
        captchaLoading.value = false;
      }
    }, 7000);
  } catch (error) {
    console.error('获取验证码失败:', error);
    captchaLoading.value = false;
  }
}

// 供父组件调用的设置验证码方法
function setCaptchaData(imageData: string, key: string) {
  captchaImage.value = imageData;
  captchaKey.value = key;
  // 停止加载状态
  captchaLoading.value = false;
  // 清除验证码错误状态
  clearCaptchaError();
}

// UUID生成所需的16进制字符
const hexList = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9', 'a', 'b', 'c', 'd', 'e', 'f'];

function buildUUID(): string {
  let uuid = '';
  for (let i = 1; i <= 36; i++) {
    if (i === 9 || i === 14 || i === 19 || i === 24) {
      uuid += '-';
    } else if (i === 15) {
      uuid += 4;
    } else if (i === 20) {
      uuid += hexList[(Math.random() * 4) | 8];
    } else {
      uuid += hexList[(Math.random() * 16) | 0];
    }
  }
  return uuid.replace(/-/g, '');
}

// 验证验证码
function validateCaptcha() {
  showCaptchaError.value = false;
  captchaError.value = '';

  if (!captchaCode.value) {
    captchaError.value = '请输入验证码';
    showCaptchaError.value = true;
    return false;
  }

  if (captchaCode.value.length < 4) {
    captchaError.value = '验证码长度不正确';
    showCaptchaError.value = true;
    return false;
  }

  return true;
}

// 清除验证码错误状态
function clearCaptchaError() {
  if (showCaptchaError.value) {
    showCaptchaError.value = false;
    captchaError.value = '';
  }
}

async function handleSubmit() {
  const { valid } = await formApi.validate();
  const values = await formApi.getValues();

  // 验证码校验
  const captchaValid = validateCaptcha();

  if (!captchaValid) {
    return;
  }

  if (valid) {
    localStorage.setItem(
      REMEMBER_ME_KEY,
      rememberMe.value ? values?.username : '',
    );

    // 提交时包含验证码信息
    const submitData = {
      ...values,
      captcha: captchaCode.value,
      checkKey: captchaKey.value,
    };

    emit('submit', submitData);
  }
}

function handleGo(path: string) {
  router.push(path);
}

const isQRCodeVisible = ref(false);
const isFormVisible = ref(true); // 表单默认显示
// 钉钉登录
const isDingdingLogin = ref(false);
const loadingMask = ref(false);



// 手机号登录图片验证码
const mobileCaptchaCode = ref('');
const mobileCaptchaImage = ref('');
const mobileCaptchaKey = ref('');
const mobileCaptchaLoading = ref(false);
const mobileCaptchaError = ref('');
const showMobileCaptchaError = ref(false);

// 获取手机号登录图形验证码
async function getMobileCaptcha() {
  try {
    // 开始加载
    mobileCaptchaLoading.value = true;

    // 清空当前输入的验证码和图片
    mobileCaptchaCode.value = '';
    mobileCaptchaImage.value = '';

    // 清除验证码错误状态
    clearMobileCaptchaError();

    // 向父组件发送获取验证码的事件
    emit('getMobileCaptcha');

    // 如果5秒后还没有收到验证码，停止loading
    setTimeout(() => {
      if (mobileCaptchaLoading.value && !mobileCaptchaImage.value) {
        mobileCaptchaLoading.value = false;
      }
    }, 7000);
  } catch (error) {
    console.error('获取手机号登录图形验证码失败:', error);
    mobileCaptchaLoading.value = false;
  }
}

// 供父组件调用的设置手机号登录验证码方法
function setMobileCaptchaData(imageData: string, key: string) {
  mobileCaptchaImage.value = imageData;
  mobileCaptchaKey.value = key;
  // 停止加载状态
  mobileCaptchaLoading.value = false;
  // 清除验证码错误状态
  clearMobileCaptchaError();
}

// 验证手机号登录图形验证码
function validateMobileCaptcha() {
  showMobileCaptchaError.value = false;
  mobileCaptchaError.value = '';

  if (!mobileCaptchaCode.value) {
    mobileCaptchaError.value = '请输入图形验证码';
    showMobileCaptchaError.value = true;
    return false;
  }

  if (mobileCaptchaCode.value.length < 4) {
    mobileCaptchaError.value = '图形验证码长度不正确';
    showMobileCaptchaError.value = true;
    return false;
  }

  return true;
}

// 清除手机号登录验证码错误状态
function clearMobileCaptchaError() {
  if (showMobileCaptchaError.value) {
    showMobileCaptchaError.value = false;
    mobileCaptchaError.value = '';
  }
}
function handleBackToForm() {
  isQRCodeVisible.value = false; // 隐藏二维码
  isFormVisible.value = true;    // 显示表单
}
// 钉钉登录页面跳转
function handleDingGo() {
  loadDDLogin()
  isQRCodeVisible.value = true; // 显示二维码
  isFormVisible.value = false;  // 隐藏表单
  // window.location.assign('https://oapi.dingtalk.com/connect/qrconnect?appid=dingytnirhml8xnwfepi&response_type=code&scope=snsapi_login&state=STATE&redirect_uri=http%3A%2F%2F10.159.222.195%3A5888%2Fauth%2Flogin')
  // window.location.assign('https://login.dingtalk.com/oauth2/auth?client_id=dingytnirhml8xnwfepi&response_type=code&scope=openid&prompt=consent&redirect_uri=http%3A%2F%2F10.159.222.195%3A5888%2Fauth%2Flogin')
  //window.open('https://login.dingtalk.com/oauth2/auth?client_id=dingyjonvrl8olfxsdxv&response_type=code&scope=openid&prompt=consent&state=STATE&redirect_uri=http://192.168.86.111:5888/eel-boot/auth/loginDing', '_blank', 'width=600,height=600,left=500,top=300,resizable=yes,scrollbars=yes,status=yes');
}

function dingdingLogin() {
  isDingdingLogin.value = !isDingdingLogin.value;
  if(isDingdingLogin.value) {
    loadingMask.value = true;
    loadDDLogin();
  } else {
    loadingMask.value = false;
  }
}

// 手机号登录切换
function mobileLogin() {
  isMobileLogin.value = !isMobileLogin.value;
  if (isMobileLogin.value) {
    // 切换到手机号登录时，关闭钉钉登录
    isDingdingLogin.value = false;
    loadingMask.value = false;
    // 重置验证码相关状态
    mobileVerifyCode.value = '';
    verifyCodeTouched.value = false;
    showVerifyCodeError.value = false;
    verifyCodeError.value = '';
    // 重置图形验证码相关状态
    mobileCaptchaCode.value = '';
    showMobileCaptchaError.value = false;
    mobileCaptchaError.value = '';
    // 获取图形验证码
    getMobileCaptcha();
  }
}

// 手机号登录提交
async function handleMobileSubmit() {
  const { valid } = await mobileFormApi.validate();
  const values = await mobileFormApi.getValues();
  
  // 验证图形验证码
  const captchaValid = validateMobileCaptcha();
  
  // 验证短信验证码（强制验证）
  const verifyCodeValid = validateVerifyCode(true);
  
  if (valid && captchaValid && verifyCodeValid) {
    const submitData = {
      phoneNumber: values.mobileNumber,
      smsCode: mobileVerifyCode.value,
      captcha: mobileCaptchaCode.value,
      checkKey: mobileCaptchaKey.value,
      loginType: 'mobile'
    };
    
    emit('submit', submitData);
  }
}

//加载钉钉页面
async function loadDDLogin() {

  if (typeof dd !== 'undefined' && dd.version) {
      console.log("当前在钉钉环境中");

      return;
  } else {
      console.log("不在钉钉环境中");
  }

  // 获取URL中的redirect参数
  const urlParams = new URLSearchParams(window.location.search);
  const redirectParam = urlParams.get('redirect');
  
  // 构建redirect_uri，如果有redirect参数则拼接
  let redirectUri = import.meta.env.VITE_APP_API_BASE_URL + 'auth/login';
  if (redirectParam) {
    // 解码redirect参数（因为URL中的redirect是双重编码的）
    const decodedRedirect = decodeURIComponent(redirectParam);
    redirectUri += `?redirect=${encodeURIComponent(decodedRedirect)}`;
  }

  console.log('Received encodeURIComponent:',  `${encodeURIComponent(import.meta.env.VITE_APP_API_BASE_URL + 'auth/login')}`);
  const goto = encodeURIComponent(
        `https://oapi.dingtalk.com/connect/qrconnect?appid=${import.meta.env.VITE_APP_ID}&response_type=code&scope=snsapi_login&state=STATE&redirect_uri=${encodeURIComponent(redirectUri)}`
    );
    const script = document.createElement('script');
    script.src = 'https://g.alicdn.com/dingding/dinglogin/0.0.5/ddLogin.js';
    script.onload = () => {
      if (window.DDLogin) {
        window.DDLogin({
          id: 'login_container',
          goto: goto,
          style: 'border:none;background-color:transparent;',
          width: '365',
          height: '300',
        });

      } else {
        console.error('DDLogin function is not available');

      }
    };
    document.head.appendChild(script);

    // 监听来自钉钉的loginTmpCode
    window.addEventListener('message', (event: MessageEvent) => {
      if (event.origin === 'https://login.dingtalk.com') {

        const loginTmpCode = event.data;
        console.log('Received loginTmpCode:', loginTmpCode);

        // 构建授权URL，同样需要处理redirect参数
        let authorizeRedirectUri = import.meta.env.VITE_APP_API_BASE_URL + 'auth/login';
        if (redirectParam) {
          const decodedRedirect = decodeURIComponent(redirectParam);
          authorizeRedirectUri += `?redirect=${encodeURIComponent(decodedRedirect)}`;
        }

        const authorizeUrl = `https://oapi.dingtalk.com/connect/oauth2/sns_authorize?appid=AppKey&response_type=code&scope=snsapi_login&state=STATE&redirect_uri=${encodeURIComponent(authorizeRedirectUri)}&loginTmpCode=${loginTmpCode}`;

        // 跳转到钉钉的授权页面
        window.location.href = authorizeUrl;



  //       if (!loginTmpCode || loginTmpCode === 'undefined' || loginTmpCode === 'null' || loginTmpCode === '') {
  //         MessagePlugin.error('登录授权失败，请重新尝试');
  //         // 获取到loginTmpCode后可以进行下一步操作

  //       } else {

  //         const authorizeUrl = `https://oapi.dingtalk.com/connect/oauth2/sns_authorize?appid=${import.meta.env.VITE_APP_ID}&response_type=code&scope=snsapi_login&state=STATE&redirect_uri=${encodeURIComponent(import.meta.env.VITE_APP_API_BASE_URL + 'auth/login')}&loginTmpCode=${loginTmpCode}`;
  //         // 跳转到钉钉的授权页面
  //         window.location.href = authorizeUrl;
  //         // emit('submit', {dtCode: loginTmpCode});
  //         // 获取到loginTmpCode后可以进行下一步操作
  //         // const authorizeUrl = `https://oapi.dingtalk.com/connect/oauth2/sns_authorize?appid=${import.meta.env.VITE_APP_ID}&response_type=code&scope=snsapi_login&state=STATE&redirect_uri=${encodeURIComponent(import.meta.env.VITE_APP_API_BASE_URL + 'auth/login')}&loginTmpCode=${loginTmpCode}`;
  //         // 跳转到钉钉的授权页面
  //         // window.location.href = authorizeUrl;
  //       }
      }
    });
    setTimeout(() => {
      loadingMask.value = false;
    }, 100);
  }

onMounted(() => {
  if (localUsername) {
    formApi.setFieldValue('username', localUsername);
  }

  // 初始化验证码
  getCaptcha();
});

// 处理发送验证码的结果
function handleSmsCodeResult(result: any) {
  smsLoading.value = false;
  
  if (result && result.result === 'success') {
    MessagePlugin.success('验证码已发送，请注意查收');
    // 发送成功，开始倒计时
    startSmsCountdown();
  } else if (result && result.result === 'test') {
    MessagePlugin.success('验证码已发送，请注意查收');
    // 测试模式，自动填充验证码并开始倒计时
    mobileVerifyCode.value = result.smsCode;
    startSmsCountdown();
  } else {
    // 发送失败，不开始倒计时，保持可点击状态
  }
}

defineExpose({
  getFormApi: () => formApi,
  setCaptchaData,
  getCaptcha,
  setMobileCaptchaData,
  getMobileCaptcha,
  handleSmsCodeResult,
});
</script>

<template>
  <div @keydown.enter.prevent="handleSubmit">
    <slot name="title">
      <Title>
        <slot name="title">
          {{ title || `${$t('authentication.welcomeBack')} 👋🏻` }}
        </slot>
        <template #desc>
          <span class="text-muted-foreground">
            <slot name="subTitle">
              {{ subTitle || $t('authentication.loginSubtitle') }}
            </slot>
          </span>
        </template>
      </Title>
    </slot>

    <Form v-if="!isDingdingLogin && !isMobileLogin"/>

    <!-- 验证码输入区域 -->
    <div v-if="!isDingdingLogin && !isMobileLogin" class="captcha-container">
      <div class="flex items-start space-x-2">
        <div class="flex-1 captcha-input-wrapper">
          <input
            v-model="captchaCode"
            type="text"
            placeholder="请输入验证码"
            :class="[
              'border-input bg-background ring-offset-background placeholder:text-muted-foreground focus-visible:ring-ring flex h-10 w-full rounded-md border px-3 py-2 text-sm file:border-0 file:bg-transparent file:text-sm file:font-medium focus-visible:outline-none focus-visible:ring-1 disabled:cursor-not-allowed disabled:opacity-50',
              {
                'border-red-500 ': showCaptchaError
              }
            ]"
            maxlength="4"
            @input="clearCaptchaError"
            @blur="validateCaptcha"
            @keydown.enter.prevent="handleSubmit"
          />
          <!-- 验证码错误提示 - 固定高度区域 -->
          <div class="captcha-error-area">
            <div v-if="showCaptchaError" class="captcha-error-message">
              {{ captchaError }}
            </div>
          </div>
        </div>
        <div class="flex items-start justify-center w-min-width captcha-image-wrapper">
          <!-- 验证码图片 -->
          <img
            v-if="captchaImage && !captchaLoading"
            :src="captchaImage"
            alt="验证码"
            class="captcha-image h-10 w-30 cursor-pointer border border-input rounded"
            title="点击刷新验证码"
            @click="getCaptcha"
          />

          <!-- 验证码加载中 -->
          <div
            v-if="captchaLoading"
            class="captcha-loading h-10 w-30 border border-input rounded flex items-center justify-center cursor-pointer w-min-width"
            title="点击刷新验证码"
            @click="getCaptcha"
          >
            <div class="spinner"></div>
          </div>

          <!-- 验证码占位符 -->
          <div
            v-else-if="!captchaImage"
            class="captcha-placeholder h-10 w-30 border border-input rounded flex items-center justify-center cursor-pointer w-min-width"
            title="点击获取验证码"
            @click="getCaptcha"
          >
            <span class="text-xs text-gray-400">点击获取</span>
          </div>

        </div>
      </div>
    </div>

    <div v-if="isDingdingLogin" class="flex flex-col items-center justify-center" style="position:relative;">
      <div class="mb-4 text-center" style="position:relative;">
        <!-- loading 遮罩层 -->
        <div v-if="loadingMask" class="qr-loading-mask" style="pointer-events: auto;">
          <div class="qr-loading-spinner"></div>
        </div>
        <!-- 二维码显示区域 -->
        <div id="login_container" class="mb-4" style="position:relative;z-index:1;"/>
      </div>
    </div>

    <!-- 手机号登录表单 -->
    <div v-if="isMobileLogin" class="mb-4">
      <!-- 手机号输入框 -->
      <div>
        <MobileForm />
      </div>
      
      <!-- 手机号登录图形验证码 -->
      <div class="captcha-container">
        <div class="flex items-start space-x-2">
          <div class="flex-1 captcha-input-wrapper">
            <input
              v-model="mobileCaptchaCode"
              type="text"
              placeholder="请输入图形验证码"
              :class="[
                'border-input bg-background ring-offset-background placeholder:text-muted-foreground focus-visible:ring-ring flex h-10 w-full rounded-md border px-3 py-2 text-sm file:border-0 file:bg-transparent file:text-sm file:font-medium focus-visible:outline-none focus-visible:ring-1 disabled:cursor-not-allowed disabled:opacity-50',
                {
                  'border-red-500': showMobileCaptchaError
                }
              ]"
              maxlength="4"
              @input="clearMobileCaptchaError"
              @blur="() => validateMobileCaptcha()"
            />
            <!-- 验证码错误提示 -->
            <div class="captcha-error-area">
              <div v-if="showMobileCaptchaError" class="captcha-error-message">
                {{ mobileCaptchaError }}
              </div>
            </div>
          </div>
          <div class="flex items-start justify-center w-min-width captcha-image-wrapper">
            <!-- 验证码图片 -->
            <img
              v-if="mobileCaptchaImage && !mobileCaptchaLoading"
              :src="mobileCaptchaImage"
              alt="图形验证码"
              class="captcha-image h-10 w-30 cursor-pointer border border-input rounded"
              title="点击刷新验证码"
              @click="getMobileCaptcha"
            />

            <!-- 验证码加载中 -->
            <div
              v-if="mobileCaptchaLoading"
              class="captcha-loading h-10 w-30 border border-input rounded flex items-center justify-center cursor-pointer w-min-width"
              title="点击刷新验证码"
              @click="getMobileCaptcha"
            >
              <div class="spinner"></div>
            </div>

            <!-- 验证码占位符 -->
            <div
              v-else-if="!mobileCaptchaImage"
              class="captcha-placeholder h-10 w-30 border border-input rounded flex items-center justify-center cursor-pointer w-min-width"
              title="点击获取验证码"
              @click="getMobileCaptcha"
            >
              <span class="text-xs text-gray-400">点击获取</span>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 短信验证码输入框和发送按钮 -->
      <div class="mb-4 captcha-container">
        <div class="flex items-start space-x-2">
          <div class="flex-1 captcha-input-wrapper">
            <input
              v-model="mobileVerifyCode"
              type="text"
              placeholder="请输入短信验证码"
              :class="[
                'border-input bg-background ring-offset-background placeholder:text-muted-foreground focus-visible:ring-ring flex h-10 w-full rounded-md border px-3 py-2 text-sm file:border-0 file:bg-transparent file:text-sm file:font-medium focus-visible:outline-none focus-visible:ring-1 disabled:cursor-not-allowed disabled:opacity-50',
                {
                  'border-red-500': showVerifyCodeError
                }
              ]"
              maxlength="6"
              @input="handleVerifyCodeInput"
              @blur="() => validateVerifyCode()"
              @keydown.enter.prevent="handleMobileSubmit"
            />
            <!-- 验证码错误提示 -->
            <div class="captcha-error-area">
              <div v-if="showVerifyCodeError" class="captcha-error-message">
                {{ verifyCodeError }}
              </div>
            </div>
          </div>
          <button
            :disabled="smsCountdown > 0 || smsLoading"
            :class="[
              'px-3 py-2 text-sm rounded border transition-colors duration-200 whitespace-nowrap w-min-width',
              smsCountdown > 0 || smsLoading
                ? 'bg-gray-100 text-gray-400 cursor-not-allowed border-gray-200'
                : 'bg-blue-500 text-white commom-button-500 cursor-pointer'
            ]"
            @click="sendSmsCode"
          >
            {{ smsLoading ? '发送中...' : smsText }}
          </button>
        </div>
      </div>
      
      <!-- 登录按钮 -->
      <VbenButton
        :class="{
          'cursor-wait': loading,
        }"
        :loading="loading"
        aria-label="mobile-login"
        class="w-full mb-4"
        @click="handleMobileSubmit"
      >
        登录
      </VbenButton>
      
      <!-- 返回账号登录 -->
      <VbenButton
        variant="outline"
        class="w-full login-button-hover"
        @click="mobileLogin"
      >
        返回账号登录
      </VbenButton>
    </div>

    <div
      v-if="(showRememberMe || showForgetPassword) && !isDingdingLogin && !isMobileLogin"
      class="mb-6 flex justify-between"
    >
      <div class="flex-center">
        <VbenCheckbox
          v-if="showRememberMe"
          v-model:checked="rememberMe"
          name="rememberMe"
        >
          {{ $t('authentication.rememberMe') }}
        </VbenCheckbox>
      </div>

      <span
        v-if="showForgetPassword"
        class="vben-link text-sm font-normal"
        @click="handleGo(forgetPasswordPath)"
      >
        {{ $t('authentication.forgetPassword') }}
      </span>
    </div>
    <VbenButton
      v-if="!isDingdingLogin && !isMobileLogin"
      :class="{
        'cursor-wait': loading,
      }"
      :loading="loading"
      aria-label="login"
      class="w-full"
      @click="handleSubmit"
    >
      {{ submitButtonText || $t('common.login') }}
    </VbenButton>
    <VbenButton
      v-if="isDingdingLogin"

      :loading="loading"
      aria-label="login"
      class="w-full login-button-hover"
      variant="outline"
      @click="dingdingLogin"
    >
      返回账号登录
    </VbenButton>
    <!-- 钉钉登录和手机号登录按钮 - 一行两个 -->
    <div v-if="!isDingdingLogin && !isMobileLogin" class="flex gap-2 w-margin">
      <VbenButton
        class="flex-1 login-button-hover"
        :loading="loading"
        variant="outline"
        @click="dingdingLogin"
      >
        钉钉登录
      </VbenButton>
      
      <VbenButton
        class="flex-1 login-button-hover"
        :loading="loading"
        variant="outline"
        @click="mobileLogin"
      >
        手机号登录
      </VbenButton>
    </div>

    <!-- 立即注册 -->
    <slot name="to-register" v-if="!isDingdingLogin && !isMobileLogin">
      <div v-if="showRegister" class="mt-4 text-center text-sm">
        <span class="text-muted-foreground">还没有账号？</span>
        <span
          class="vben-link text-sm font-normal ml-1"
          @click="handleGo(registerPath)"
        >
          立即注册
        </span>
      </div>
    </slot>

    <!-- <div
      v-if="showCodeLogin || showQrcodeLogin"
      class="mb-2 mt-4 flex items-center justify-between"
    >
      <VbenButton
        v-if="showCodeLogin"
        class="w-1/2"
        variant="outline"
        @click="handleGo(codeLoginPath)"
      >
        {{ $t('authentication.mobileLogin') }}
      </VbenButton>
      <VbenButton
        v-if="showQrcodeLogin"
        class="ml-4 w-1/2"
        variant="outline"
        @click="handleGo(qrCodeLoginPath)"
      >
        {{ $t('authentication.qrcodeLogin') }}
      </VbenButton>
    </div> -->

    <!-- 第三方登录 -->
    <!-- <slot name="third-party-login">
      <ThirdPartyLogin v-if="showThirdPartyLogin" />
    </slot> -->

  </div>
</template>
<style lang="scss" scoped>
.commom-button-500 {
  background-color: hsl(var(--primary));
}

.login-button-hover {
  transition: all 0.3s ease;
  transform: translateY(0);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    border-color: hsl(var(--primary));
    color: hsl(var(--primary));
  }
  
  &:active {
    transform: translateY(0);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }
}

.w-margin {
  margin-top: 10px;
}
.w-min-width {
  min-width: 116px;
}
.qr-loading-mask {
  position: absolute;
  top: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 365px;
  height: 300px;
  background: rgba(255,255,255,0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
}
.qr-loading-spinner {
  border: 3px solid #f3f3f3;
  border-top: 3px solid hsl(var(--primary));
  border-radius: 50%;
  width: 36px;
  height: 36px;
  animation: spin 1s linear infinite;
}
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 验证码相关样式 */
.captcha-container {
  .captcha-input-wrapper {
    // 为输入框包装器设置固定结构
    display: flex;
    flex-direction: column;
  }

  .captcha-error-area {
    // 固定高度的错误提示区域，防止布局跳动
    height: 24px; // 预留错误提示的空间
    margin-top: 4px;
    display: flex;
    align-items: flex-start;
  }

  .captcha-error-message {
    font-size: 0.875rem; // text-sm
    color: #ef4444; // text-red-500
    line-height: 1.25rem;
    animation: fadeIn 0.2s ease-in-out;
  }

  .captcha-image-wrapper {
    // 验证码图片容器，确保与输入框顶部对齐
    padding-top: 0;
    min-height: 40px; // 与输入框高度保持一致
  }

  .captcha-image {
    transition: opacity 0.2s ease;

    &:hover {
      opacity: 0.8;
    }
  }

  .refresh-btn {
    transition: color 0.2s ease;

    &:hover {
      color: hsl(var(--primary));
    }
  }

  .captcha-loading {
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    transition: all 0.2s ease;

    &:hover {
      background: linear-gradient(135deg, #c3cfe2 0%, #f5f7fa 100%);
    }
  }

  .captcha-placeholder {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    transition: all 0.2s ease;

    &:hover {
      background: linear-gradient(135deg, #764ba2 0%, #667eea 100%);
    }

    span {
      color: white !important;
    }
  }

  .spinner {
    width: 20px;
    height: 20px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #0052d9;
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }

  // 验证码输入框错误状态
  input.border-red-500 {
    border-color: #ef4444 !important;

    &:focus, &:focus-visible {
      outline: none;
      ring: 2px;
      ring-color: rgba(239, 68, 68, 0.2);
      border-color: #ef4444;
    }
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@keyframes fadeIn {
  0% {
    opacity: 0;
    transform: translateY(-4px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>

