<script setup lang="ts">
import {
  HeartIcon,
  ShareIcon,
  ViewListIcon,
  ViewModuleIcon,
  CalculatorIcon,
  ChevronLeftIcon
} from 'tdesign-icons-vue-next';
import {
  Button,
  Checkbox,
  CheckboxGroup,
  Empty,
  Loading,
  Pagination,
  Select,
  Table,
  Tag,
  Dialog
} from 'tdesign-vue-next';
import { computed, h, onMounted, reactive, ref } from 'vue';

import {
  getCodmDiatomicSymbolsUsedInfo,
  getCodmDiatomicConstantsFor12c16oInfo
} from '../api';
import { useSearchStore } from '#/store/search';

interface CodmDiatomicInfo {
  symbol: string;
  meaning: string;
}
interface CodmDiatomic12c16oInfo {
  casRegistryNumber?: string; // CAS注册号
  state?: string;             // 版权
  te?: string;                // Te
  omegae?: string;            // ωe
  omegaexe?: string;          // ωexe
  omegaeye?: string;          // ωeye
  be?: string;                // Be
  alphae?: string;            // αe
  gammae?: string;            // γe
  de?: string;                // De
  betae?: string;             // βe
  re?: string;                // re
  trans?: string;             // Trans
  nu00?: string;              // ν00
}

/**
 * 内部静态数据定义
 */
const state: any = reactive({
  /**
   * 当前详情数据
   */
  detailItem: {},
  /**
  * 加载状态
  */
  loading: true,

});
// 响应式数据
const codmDiatomicList = ref<CodmDiatomicInfo[]>([]);
const CodmDiatomic12c16oList = ref<CodmDiatomic12c16oInfo[][]>([]);
// 表格列定义
const getCodmDiatomicColumns = () => [
  {
    colKey: 'symbol',
    title: 'Symbol',
    width: 100,
    ellipsis: true,
    cell: (h, { row }) => h('span', { innerHTML: row.symbol })
  },
  {
    colKey: 'meaning',
    title: 'Meaning',
    width: 300,
    ellipsis: true,
    cell: (h, { row }) => h('span', { innerHTML: row.meaning })
  },
];

const codmDiatomicColumns = [
  {
    colKey: 'id',
    title: 'ID',
  },
  {
    colKey: 'state',
    title: 'State',
    width: 120,
    ellipsis: true,
    cell: (h, { row }) => {
      // 如果 state 为空且 te 不为空，显示 te
      if ((!row.state || row.state === '') && row.te) {
        return h('span', { innerHTML: row.te });
      }
      // 其它情况显示原 state
      return h('span', { innerHTML: row.state });
    }
  },
  {
    colKey: 'te',
    title: (h) => h('span', { innerHTML: 'T<sub>e</sub>' }),
    width: 100,
    ellipsis: true,
    cell: (h, { row }) => h('span', { innerHTML: row.te })
  },
  {
    colKey: 'omegae',
    title: (h) => h('span', { innerHTML: 'ω<sub>e</sub>' }),
    width: 100,
    ellipsis: true,
    cell: (h, { row }) => h('span', { innerHTML: row.omegae })
  },
  {
    colKey: 'omegaexe',
    title: (h) => h('span', { innerHTML: 'ω<sub>e</sub>x<sub>e</sub>' }),
    width: 100,
    ellipsis: true,
    cell: (h, { row }) => h('span', { innerHTML: row.omegaexe })
  },
  {
    colKey: 'omegaeye',
    title: (h) => h('span', { innerHTML: 'ω<sub>e</sub>y<sub>e</sub>' }),
    width: 100,
    ellipsis: true,
    cell: (h, { row }) => h('span', { innerHTML: row.omegaeye })
  },
  {
    colKey: 'be',
    title: (h) => h('span', { innerHTML: 'B<sub>e</sub>' }),
    width: 100,
    ellipsis: true,
    cell: (h, { row }) => h('span', { innerHTML: row.be })
  },
  {
    colKey: 'alphae',
    title: (h) => h('span', { innerHTML: 'α<sub>e</sub>' }),
    width: 100,
    ellipsis: true,
    cell: (h, { row }) => h('span', { innerHTML: row.alphae })
  },
  {
    colKey: 'gammae',
    title: (h) => h('span', { innerHTML: 'γ<sub>e</sub>' }),
    width: 100,
    ellipsis: true,
    cell: (h, { row }) => h('span', { innerHTML: row.gammae })
  },
  {
    colKey: 'de',
    title: (h) => h('span', { innerHTML: 'D<sub>e</sub>' }),
    width: 100,
    ellipsis: true,
    cell: (h, { row }) => h('span', { innerHTML: row.de })
  },
  {
    colKey: 'betae',
    title: (h) => h('span', { innerHTML: 'β<sub>e</sub>' }),
    width: 100,
    ellipsis: true,
    cell: (h, { row }) => h('span', { innerHTML: row.betae })
  },
  {
    colKey: 're',
    title: (h) => h('span', { innerHTML: 'r<sub>e</sub>' }),
    width: 100,
    ellipsis: true,
    cell: (h, { row }) => h('span', { innerHTML: row.re })
  },
  {
    colKey: 'trans',
    title: 'Trans.',
    width: 120,
    ellipsis: true,
    cell: (h, { row }) => h('span', { innerHTML: row.trans })
  },
  {
    colKey: 'nu00',
    title: (h) => h('span', { innerHTML: 'ν<sub>00</sub>' }),
    width: 100,
    ellipsis: true,
    cell: (h, { row }) => h('span', { innerHTML: row.nu00 })
  },
];

const displayColumns = ref(['state', 'te', 'omegae', 'omegaexe', 'omegaeye', 'be', 'alphae', 'gammae', 'de', 'betae', 're', 'trans', 'nu00']);
const mergeColumns = [
  'te', 'omegae', 'omegaexe', 'omegaeye', 'be', 'alphae', 'gammae', 'de', 'betae', 're', 'trans', 'nu00'
];

// Store
const searchStore = useSearchStore();
// 获取当前详情数据
state.detailItem = computed(() => {
  // 获取当前详情数据
  const detail = searchStore.currentDetailItem;
  // 如果当前详情数据存在，则返回当前详情数据
  if (detail) {
    // 返回当前详情数据
    return detail;
  } else {
    // 如果当前详情数据不存在，则从本地存储中获取当前详情数据
    const local = localStorage.getItem('currentDetailItem');
    // 如果本地存储中不存在当前详情数据，则返回空对象
    return JSON.parse(local ?? '{}');
  }
});

onMounted(async () => {
  try {
    // 调用双原子分子常数中使用的符号信息查询API
    const response = await getCodmDiatomicSymbolsUsedInfo({ iupacStandardInchiKey: state.detailItem.baseCode });
    codmDiatomicList.value = response;
    const res = await getCodmDiatomicConstantsFor12c16oInfo({ iupacStandardInchiKey: state.detailItem.baseCode });
    CodmDiatomic12c16oList.value = res;
  } catch (error) {
    console.error('获取双原子分子常数信息失败:', error);
  } finally {
    // 关闭加载状态
    state.loading = false;
  }
});

function mergeStateCells({ row, col, rowIndex, colIndex }, data) {
  // ① state为空，te不为空，合并除第一列外的所有列，值显示在第一列
  if ((!row.state || row.state === '') && row.te) {
    if (colIndex === 0) {
      return { rowspan: 1, colspan: mergeColumns.length + 1 }; // 第一列合并后面所有列
    } else {
      return { rowspan: 0, colspan: 0 }; // 其余列隐藏
    }
  }

  // ② te不为空，其余字段都为空，合并除第一列外的整行
  const allEmpty = mergeColumns.slice(1).every(key => !row[key]);
  if (row.te && allEmpty) {
    if (colIndex === 0) {
      return { rowspan: 1, colspan: 1 }; // 第一列单独
    } else if (colIndex === 1) {
      return { rowspan: 1, colspan: mergeColumns.length }; // 合并后面所有列
    } else {
      return { rowspan: 0, colspan: 0 };
    }
  }

  if (col.colKey !== 'state') return { rowspan: 1, colspan: 1 };
  if (rowIndex > 0 && row.state === data[rowIndex - 1].state) {
    return { rowspan: 0, colspan: 0 };
  }
  let rowspan = 1;
  for (let i = rowIndex + 1; i < data.length; i++) {
    if (data[i].state === row.state) {
      rowspan++;
    } else {
      break;
    }
  }
  return { rowspan: rowspan, colspan: 1 };
}
</script>

<template>
  <div class="codmDiatomic-info">
    <h1>Constants of diatomic molecules</h1>
    <div v-if="codmDiatomicList && codmDiatomicList.length > 0">
      <h2>Symbols used in the table of constants</h2>
      <Table :data="codmDiatomicList" :columns="getCodmDiatomicColumns()" :bordered="true" resizable :hover="true"
        :stripe="true" row-key="id" table-layout="fixed" cell-empty-content="-"
        style="border:1px solid #e6e8eb;margin-top: 10px;width: 50%;" />
    </div>
    <div v-if="CodmDiatomic12c16oList && CodmDiatomic12c16oList.length > 0">
      <br />
      <br />
      <h3>Diatomic constants for {{ state.detailItem.dataName }}</h3>
      <div class="codm-diatomic-tables">
        <div v-for="(group, groupIdx) in CodmDiatomic12c16oList" :key="groupIdx" class="codm-diatomic-group">
          <!-- 表格 -->
          <Table :data="group" :columns="codmDiatomicColumns" :displayColumns="displayColumns" row-key="id"
            :rowspan-and-colspan="raw => mergeStateCells(raw, group)" style="border:1px solid #e6e8eb" :bordered="true"
            resizable :hover="true" :stripe="true" table-layout="fixed" cell-empty-content="-" />
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
.codmDiatomic-info {
  h1 {
    font-size: 24px;
    margin-bottom: 10px;
  }

  h2 {
    width: 50%;
    font-size: 20px;
    margin-bottom: 10px;
    text-align: center;
  }

  h3 {
    font-size: 16px;
    text-align: center;
  }

  .codm-diatomic-tables {
    .group-title {
      background: #f8d7da;
      color: #721c24;
      padding: 8px 12px;
      margin-bottom: 0;
      font-weight: bold;
      border-radius: 4px 4px 0 0;
    }

    .codm-diatomic-group {
      margin-bottom: 32px;
    }
  }

  :deep(.t-table td),
  :deep(.t-table th) {
    border-right: 1px solid #e6e8eb !important;
  }

  :deep(.t-table td:last-child),
  :deep(.t-table th:last-child) {
    border-right: none !important;
  }

  :deep(.t-table td) {
    max-height: 2.8em !important;
    line-height: 1.4 !important;
    padding: 8px !important;
    overflow: hidden !important;
    word-break: break-word !important;
  }
}
</style>
