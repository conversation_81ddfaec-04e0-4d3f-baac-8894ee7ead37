import { requestClient } from '#/api/request';

// 数据汇聚与清洗相关API接口

/**
 * 创建汇聚任务
 * @param data 任务数据
 */
export async function createTaskAll(data: FormData) {
  return requestClient.post('/rgdc-sys/dataTools/cleaning/create_task_all', data, {
    headers: { 'Content-Type': 'multipart/form-data' },
    timeout: 60_000,
  });
}

/**
 * 获取汇聚任务列表
 * @param params 查询参数
 */
export async function getGatherTasks(params: { page: number; page_size: number }) {
  return requestClient.post('/rgdc-sys/dataTools/cleaning/get_gather_tasks', params);
}

/**
 * 上传文件
 * @param file 文件
 */
export async function uploadFile(file: FormData) {
  return requestClient.post('/rgdc-sys/dataTools/cleaning/upload_file', file, {
    headers: { 'Content-Type': 'multipart/form-data' },
  });
}

/**
 * 获取任务详情
 * @param taskId 任务ID
 */
export async function getTaskDetail(taskId: string) {
  return requestClient.get(`/rgdc-sys/dataTools/cleaning/task_detail/${taskId}`);
}

/**
 * 删除任务
 * @param taskId 任务ID
 */
export async function deleteTask(taskId: string) {
  return requestClient.delete(`/rgdc-sys/dataTools/cleaning/delete_task/${taskId}`);
}

/**
 * 重新运行任务
 * @param taskId 任务ID
 */
export async function rerunTask(taskId: string) {
  return requestClient.post(`/rgdc-sys/dataTools/cleaning/rerun_task/${taskId}`);
}

/**
 * 获取任务状态
 * @param taskId 任务ID
 */
export async function getTaskStatus(taskId: string) {
  return requestClient.get(`/rgdc-sys/dataTools/cleaning/task_status/${taskId}`);
}

/**
 * 下载任务结果
 * @param taskId 任务ID
 */
export async function downloadTaskResult(taskId: string) {
  return requestClient.get(`/rgdc-sys/dataTools/cleaning/download_result/${taskId}`, {
    responseType: 'blob',
  });
}
