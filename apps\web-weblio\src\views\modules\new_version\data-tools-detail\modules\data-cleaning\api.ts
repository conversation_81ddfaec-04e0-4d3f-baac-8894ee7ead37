import { request } from '@/utils/request';

// 数据汇聚与清洗相关API接口

/**
 * 创建汇聚任务
 * @param data 任务数据
 */
export function createTaskAll(data: FormData) {
  return request({
    url: '/gather_api/create_task_all',
    method: 'POST',
    data,
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });
}

/**
 * 获取汇聚任务列表
 * @param params 查询参数
 */
export function getGatherTasks(params: { page: number; page_size: number }) {
  return request({
    url: '/gather_api/get_gather_tasks',
    method: 'POST',
    data: params,
    headers: {
      'Content-Type': 'application/json;charset=utf-8',
    },
  });
}

/**
 * 上传文件
 * @param file 文件
 */
export function uploadFile(file: FormData) {
  return request({
    url: '/query_file_json',
    method: 'POST',
    data: file,
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });
}

/**
 * 获取任务详情
 * @param taskId 任务ID
 */
export function getTaskDetail(taskId: string) {
  return request({
    url: `/gather_api/task_detail/${taskId}`,
    method: 'GET',
  });
}

/**
 * 删除任务
 * @param taskId 任务ID
 */
export function deleteTask(taskId: string) {
  return request({
    url: `/gather_api/delete_task/${taskId}`,
    method: 'DELETE',
  });
}

/**
 * 重新运行任务
 * @param taskId 任务ID
 */
export function rerunTask(taskId: string) {
  return request({
    url: `/gather_api/rerun_task/${taskId}`,
    method: 'POST',
  });
}

/**
 * 获取任务状态
 * @param taskId 任务ID
 */
export function getTaskStatus(taskId: string) {
  return request({
    url: `/gather_api/task_status/${taskId}`,
    method: 'GET',
  });
}

/**
 * 下载任务结果
 * @param taskId 任务ID
 */
export function downloadTaskResult(taskId: string) {
  return request({
    url: `/gather_api/download_result/${taskId}`,
    method: 'GET',
    responseType: 'blob',
  });
}
