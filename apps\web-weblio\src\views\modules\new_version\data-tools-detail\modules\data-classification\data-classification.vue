<script setup lang="ts">
import { DownloadIcon, UploadIcon } from 'tdesign-icons-vue-next';
import {
  Button,
  Card,
  Checkbox,
  CheckboxGroup,
  Col,
  Dialog,
  Form,
  FormItem,
  Input,
  Loading,
  MessagePlugin,
  Pagination,
  Row,
  Space,
  Table,
  Upload,
} from 'tdesign-vue-next';
import { computed, reactive, ref } from 'vue';
import { useRouter } from 'vue-router';

import { createClassificationTask, downloadTemplate, getTaskList } from './api';

// 路由
const router = useRouter();

// 响应式数据
const loading = ref(false);
const activeTab = ref('tab1');
const step1 = ref(true);
const taskName = ref('');
const doi = ref('');
const fileList = ref<File[]>([]);
const dialogVisible = ref(false);
const taskList = ref<any[]>([]);
const page = ref(1);
const pageSize = ref(10);
const total = ref(0);

// 表单数据
const form = reactive({
  checkList: ['论文'],
});

// 复选框数据
const checkAll1 = ref(false);
const checkedCities1 = ref<string[]>([]);
const cities1 = ref([
  { key: 'authors', label: '作者' },
  { key: 'title', label: '标题' },
  { key: 'source', label: '来源' },
  { key: 'year', label: '年份' },
]);

const checkAll2 = ref(false);
const checkedCities2 = ref([
  '地址',
  '所属机构',
  '文献类型',
  '关键词',
  'WoS 类别',
  '研究方向',
  'WoS 版本 (仅限打印)',
]);
const cities2 = ref([
  { key: 'address', label: '地址' },
  { key: 'institution', label: '所属机构' },
  { key: 'docType', label: '文献类型' },
  { key: 'keywords', label: '关键词' },
  { key: 'wosCategory', label: 'WoS 类别' },
  { key: 'researchArea', label: '研究方向' },
  { key: 'wosVersion', label: 'WoS 版本 (仅限打印)' },
]);

const checkAll3 = ref(false);
const checkedCities3 = ref<string[]>([]);
const cities3 = ref([
  { key: 'references', label: '参考文献' },
  { key: 'funding', label: '基金资助' },
  { key: 'citations', label: '引用次数' },
]);

const checkAll4 = ref(false);
const checkedCities4 = ref<string[]>([]);
const cities4 = ref([
  { key: 'doi', label: 'DOI' },
  { key: 'issn', label: 'ISSN' },
  { key: 'isbn', label: 'ISBN' },
]);

// 表格数据
const tableData = ref([
  {
    doi: '10.1001/example1',
    field1: '数据1-1',
    field2: '数据1-2',
    field3: '数据1-3',
  },
  {
    doi: '10.1002/example2',
    field1: '数据2-1',
    field2: '数据2-2',
    field3: '数据2-3',
  },
  {
    doi: '10.1003/example3',
    field1: '数据3-1',
    field2: '数据3-2',
    field3: '数据3-3',
  },
]);

// 表格列配置
const columns = [
  { colKey: 'doi', title: 'DOI', width: 180 },
  { colKey: 'field1', title: '中图法分类', width: 180 },
  { colKey: 'field2', title: '基金委学部代码' },
  { colKey: 'field3', title: '教育部学科分类' },
  {
    colKey: 'operation',
    title: '导出选项',
    cell: (h: any) => {
      return h(Space, {}, [
        h(Button, { theme: 'primary', variant: 'text' }, 'EXCEL'),
        h(Button, { theme: 'primary', variant: 'text' }, '文本文件'),
      ]);
    },
  },
];

const taskColumns = [
  { colKey: 'created_at', title: '任务时间', width: 180 },
  { colKey: 'task_name', title: '任务名称' },
  { colKey: 'file_name', title: '文件名称' },
  {
    colKey: 'operation',
    title: '操作',
    width: 180,
    cell: (h: any, { row }: any) => {
      return h(
        Button,
        {
          size: 'small',
          onClick: () => handlePreview(row),
        },
        '查看',
      );
    },
  },
];

// 计算属性
const isIndeterminate1 = computed(() => {
  const checkedCount = checkedCities1.value.length;
  return checkedCount > 0 && checkedCount < cities1.value.length;
});

const isIndeterminate2 = computed(() => {
  const checkedCount = checkedCities2.value.length;
  return checkedCount > 0 && checkedCount < cities2.value.length;
});

const isIndeterminate3 = computed(() => {
  const checkedCount = checkedCities3.value.length;
  return checkedCount > 0 && checkedCount < cities3.value.length;
});

const isIndeterminate4 = computed(() => {
  const checkedCount = checkedCities4.value.length;
  return checkedCount > 0 && checkedCount < cities4.value.length;
});

// 方法
const handleCheckAllChange = (area: number, checked: boolean) => {
  switch (area) {
    case 1: {
      checkedCities1.value = checked
        ? cities1.value.map((item) => item.key)
        : [];
      checkAll1.value = checked;

      break;
    }
    case 2: {
      checkedCities2.value = checked
        ? cities2.value.map((item) => item.key)
        : [];
      checkAll2.value = checked;

      break;
    }
    case 3: {
      checkedCities3.value = checked
        ? cities3.value.map((item) => item.key)
        : [];
      checkAll3.value = checked;

      break;
    }
    case 4: {
      checkedCities4.value = checked
        ? cities4.value.map((item) => item.key)
        : [];
      checkAll4.value = checked;

      break;
    }
    // No default
  }
};

const handleCheckedCitiesChange = (area: number, value: string[]) => {
  switch (area) {
    case 1: {
      checkedCities1.value = value;
      checkAll1.value = value.length === cities1.value.length;

      break;
    }
    case 2: {
      checkedCities2.value = value;
      checkAll2.value = value.length === cities2.value.length;

      break;
    }
    case 3: {
      checkedCities3.value = value;
      checkAll3.value = value.length === cities3.value.length;

      break;
    }
    case 4: {
      checkedCities4.value = value;
      checkAll4.value = value.length === cities4.value.length;

      break;
    }
    // No default
  }
};

// 文件上传前处理
const beforeUpload = (file: any) => {
  const isLt500M = file.size / 1024 / 1024 < 500;
  if (!isLt500M) {
    MessagePlugin.error('上传文件大小不能超过 500MB!');
    return false;
  }

  // 检查文件类型
  const allowedTypes = [
    'application/vnd.ms-excel',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
  ];
  if (
    !allowedTypes.includes(file.type) &&
    !file.name.toLowerCase().endsWith('.xlsx') &&
    !file.name.toLowerCase().endsWith('.xls')
  ) {
    MessagePlugin.error('仅支持Excel文件上传!');
    return false;
  }

  fileList.value.push(file.raw);
  MessagePlugin.success('文件上传成功！');
  return false; // 阻止自动上传
};

// 下载模板
const downLoadTemplate = async () => {
  try {
    const response = await downloadTemplate();
    // 处理下载逻辑
    const blob = new Blob([response.data]);
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = 'template.xlsx';
    document.body.append(link);
    link.click();
    link.remove();
    window.URL.revokeObjectURL(url);
    MessagePlugin.success('模板下载成功！');
  } catch {
    MessagePlugin.error('模板下载失败！');
  }
};

// 开始整编
const step1Event = async () => {
  if (!taskName.value.trim()) {
    MessagePlugin.warning('请填写任务名称');
    return;
  }

  if (!doi.value && fileList.value.length === 0) {
    MessagePlugin.error(
      '需要输入一个DOI或者上传至少一个含有DOI数据的EXCEL文件！',
    );
    return;
  }

  loading.value = true;

  try {
    const formData = new FormData();

    // 添加文件
    fileList.value.forEach((file) => {
      formData.append('excel_file', file);
    });

    // 添加参数
    formData.append('doi', doi.value);
    formData.append('task_name', taskName.value);
    formData.append(
      'selected_fields',
      JSON.stringify({
        area1: checkedCities1.value,
        area2: checkedCities2.value,
        area3: checkedCities3.value,
        area4: checkedCities4.value,
      }),
    );

    const response: any = await createClassificationTask(formData);

    if (response.code === 0) {
      MessagePlugin.success('整编任务创建成功！');
      step1.value = false;
      // 可以在这里加载结果数据
    } else {
      MessagePlugin.error(response.message || '整编失败');
    }
  } catch (error) {
    console.error('整编失败:', error);
    MessagePlugin.error('整编失败，请重试');
  } finally {
    loading.value = false;
  }
};

// 上一步
const closeStart = () => {
  step1.value = true;
};

// 打开任务列表
const startopen = async () => {
  dialogVisible.value = true;
  await getTaskData();
};

// 获取任务列表
const getTaskData = async () => {
  try {
    const params = {
      page: page.value,
      page_size: pageSize.value,
    };

    const response: any = await getTaskList(params);

    if (response.code === 0) {
      taskList.value = response.data.list || [];
      total.value = response.data.total || 0;
    }
  } catch (error) {
    console.error('获取任务列表失败:', error);
  }
};

// 预览任务
const handlePreview = (row: any) => {
  // 跳转到任务详情页面
  router.push({
    path: '/data-tools-detail/classification-result',
    query: {
      task_id: row.id,
      task_name: row.task_name,
    },
  });
};

// 分页处理
const queryCurrentChange = (current: number) => {
  page.value = current;
  getTaskData();
};

const queryHandleSizeChange = (size: number) => {
  pageSize.value = size;
  page.value = 1;
  getTaskData();
};
</script>

<template>
  <div class="data-classification-container">
    <Card class="main-card">
      <template #header>
        <div class="card-header">
          <h2 class="page-title">数据整编与分类工具</h2>
        </div>
      </template>

      <div class="description">
        <p class="description-text">
          数据整编分类工具旨在通过数据关联汇聚、抽取与采集等方法获取目标科技文献的体系化元数据，元数据字段覆盖论文基础信息、作者信息、发文期刊信息、作者所属机构信息、基金资助信息等；此外，为了满足科技文献的多样化使用场景，基于科技文献元数据，融合大模型、深度学习等智能技术对科技文献进行多体系分类。
        </p>
      </div>

      <Loading :loading="loading" show-overlay prevent-scroll-through>
        <div class="content-area">
          <div class="header-section">
            <div class="tab-buttons">
              <div
                class="tab-button"
                :class="{ active: activeTab === 'tab1' }"
                @click="activeTab = 'tab1'"
              >
                整编
              </div>
            </div>
            <Button theme="success" @click="startopen" class="task-list-btn">
              任务列表
            </Button>
          </div>

          <!-- 第一步：配置选项 -->
          <div v-if="step1" class="center">
            <Row :gutter="20">
              <!-- 左侧配置 -->
              <Col :span="5" :xs="12" :sm="12" :md="5" :lg="5" :xl="5">
                <div class="left-config">
                  <div class="checkbox-section">
                    <CheckboxGroup v-model="form.checkList">
                      <Checkbox value="论文">论文</Checkbox>
                      <Checkbox value="专利" disabled>专利</Checkbox>
                      <Checkbox value="图书" disabled>图书</Checkbox>
                    </CheckboxGroup>
                  </div>

                  <Form label-width="80px" class="config-form">
                    <FormItem label="任务名称" required>
                      <Input
                        v-model="taskName"
                        placeholder="请输入"
                        class="form-input"
                      />
                    </FormItem>

                    <FormItem label="DOI" required>
                      <Input
                        v-model="doi"
                        placeholder="请输入"
                        class="form-input"
                      />
                    </FormItem>

                    <FormItem label="DOI列表">
                      <Upload
                        theme="custom"
                        draggable
                        :before-upload="beforeUpload"
                        action="/query_file_json"
                        accept=".xlsx,.xls"
                        multiple
                        class="upload-area"
                      >
                        <template #dragContent>
                          <div class="upload-trigger">
                            <UploadIcon size="48" />
                            <div class="upload-text">
                              将文件拖到此处，或点击上传
                            </div>
                            <div class="upload-actions">
                              <Button theme="primary" @click="downLoadTemplate">
                                <template #icon>
                                  <DownloadIcon />
                                </template>
                                下载模板
                              </Button>
                            </div>
                            <div class="upload-tip">仅支持Excel文件上传</div>
                          </div>
                        </template>
                      </Upload>
                    </FormItem>
                  </Form>
                </div>
              </Col>

              <!-- 右侧字段选择 -->
              <Col :span="7" :xs="12" :sm="12" :md="7" :lg="7" :xl="7">
                <div class="right-config">
                  <div class="title-right">自定义导出选项，元数据字段：</div>

                  <div class="checkbox-big">
                    <!-- 区域1：作者和标题相关 -->
                    <div class="checkbox-li">
                      <Checkbox
                        :indeterminate="isIndeterminate1"
                        v-model="checkAll1"
                        @change="(checked) => handleCheckAllChange(1, checked)"
                      >
                        全选
                      </Checkbox>
                      <div class="divider"></div>
                      <CheckboxGroup
                        v-model="checkedCities1"
                        @change="(value) => handleCheckedCitiesChange(1, value)"
                      >
                        <Checkbox
                          v-for="item in cities1"
                          :key="item.key"
                          :value="item.key"
                        >
                          {{ item.label }}
                        </Checkbox>
                      </CheckboxGroup>
                    </div>

                    <!-- 区域2：摘要和出版相关 -->
                    <div class="checkbox-li">
                      <Checkbox
                        :indeterminate="isIndeterminate2"
                        v-model="checkAll2"
                        @change="(checked) => handleCheckAllChange(2, checked)"
                      >
                        全选
                      </Checkbox>
                      <div class="divider"></div>
                      <CheckboxGroup
                        v-model="checkedCities2"
                        @change="(value) => handleCheckedCitiesChange(2, value)"
                      >
                        <Checkbox
                          v-for="item in cities2"
                          :key="item.key"
                          :value="item.key"
                        >
                          {{ item.label }}
                        </Checkbox>
                      </CheckboxGroup>
                    </div>

                    <!-- 区域3：引用和基金相关 -->
                    <div class="checkbox-li">
                      <Checkbox
                        :indeterminate="isIndeterminate3"
                        v-model="checkAll3"
                        @change="(checked) => handleCheckAllChange(3, checked)"
                      >
                        全选
                      </Checkbox>
                      <div class="divider"></div>
                      <CheckboxGroup
                        v-model="checkedCities3"
                        @change="(value) => handleCheckedCitiesChange(3, value)"
                      >
                        <Checkbox
                          v-for="item in cities3"
                          :key="item.key"
                          :value="item.key"
                        >
                          {{ item.label }}
                        </Checkbox>
                      </CheckboxGroup>
                    </div>

                    <!-- 区域4：标识符相关 -->
                    <div class="checkbox-li">
                      <Checkbox
                        :indeterminate="isIndeterminate4"
                        v-model="checkAll4"
                        @change="(checked) => handleCheckAllChange(4, checked)"
                      >
                        全选
                      </Checkbox>
                      <div class="divider"></div>
                      <CheckboxGroup
                        v-model="checkedCities4"
                        @change="(value) => handleCheckedCitiesChange(4, value)"
                      >
                        <Checkbox
                          v-for="item in cities4"
                          :key="item.key"
                          :value="item.key"
                        >
                          {{ item.label }}
                        </Checkbox>
                      </CheckboxGroup>
                    </div>
                  </div>
                </div>
              </Col>
            </Row>
          </div>

          <!-- 第二步：结果展示 -->
          <div v-else class="center">
            <div class="export-section">
              <Button theme="success" class="export-btn"> 批量导出 </Button>
            </div>

            <Table
              :data="tableData"
              :columns="columns"
              bordered
              stripe
              class="result-table"
              :header-style="{
                backgroundColor: '#f5f7fa',
                color: '#333',
                fontWeight: 'bold',
              }"
            />
          </div>

          <!-- 操作按钮 -->
          <div class="action-section">
            <Button
              v-if="step1"
              theme="primary"
              @click="step1Event"
              :loading="loading"
              class="action-btn"
            >
              开始整编
            </Button>
            <Button
              v-if="!step1"
              theme="default"
              @click="closeStart"
              class="action-btn"
            >
              上一步
            </Button>
          </div>

          <div class="notice-section">
            <div class="download-notice">
              <span class="notice-icon">⚠</span>解构过程中请勿刷新页面
            </div>
          </div>
        </div>
      </Loading>
    </Card>

    <!-- 任务列表对话框 -->
    <Dialog v-model:visible="dialogVisible" title="任务列表" width="60%">
      <Table
        :data="taskList"
        :columns="taskColumns"
        bordered
        stripe
        class="task-table"
      />

      <template #footer v-if="total > 10">
        <Pagination
          v-model="page"
          :total="total"
          :page-size="pageSize"
          show-jumper
          @change="queryCurrentChange"
          @page-size-change="queryHandleSizeChange"
        />
      </template>
    </Dialog>
  </div>
</template>

<style scoped lang="less">
.data-classification-container {
  padding: 24px;
  background: #f5f7fa;
  min-height: 100vh;
}

.main-card {
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.card-header {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.page-title {
  margin: 0;
  font-size: 24px;
  font-weight: 600;
  color: #333;
}

.description {
  margin-bottom: 32px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 6px;
  border-left: 4px solid #0052d9;
}

.description-text {
  margin: 0;
  line-height: 1.6;
  color: #333;
  font-size: 16px;
}

.content-area {
  min-height: 600px;
}

.header-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.tab-buttons {
  display: flex;
  justify-content: left;
}

.tab-button {
  padding: 10px 33px;
  margin: 0 10px;
  cursor: pointer;
  border: 1px solid #ccc;
  border-radius: 5px;
  font-size: 16px;
  color: #333;
  background-color: #f5f5f5;
  transition: all 0.3s;

  &.active {
    background-color: #0052d9;
    color: #fff;
    border-color: #0052d9;
  }

  &:hover:not(.active) {
    background-color: #e5e5e5;
  }
}

.task-list-btn {
  margin-right: 60px;
}

.center {
  border: 1px solid #ccc;
  width: 100%;
  margin: 20px auto;
  border-radius: 20px;
  padding: 30px;
  background: #fff;
}

.left-config {
  width: 100%;
  border-right: 2px solid #808080;
  padding-right: 30px;
}

.checkbox-section {
  margin: 28px;
  margin-bottom: 20px;
}

.config-form {
  width: 100%;

  .t-form__item {
    margin-bottom: 24px;
  }
}

.form-input {
  width: 280px;
  max-width: 100%;
}

.upload-area {
  width: 300px;
  max-width: 100%;
}

.upload-trigger {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  border: 2px dashed #dcdcdc;
  border-radius: 12px;
  background: #fafafa;
  cursor: pointer;
  transition: all 0.3s ease;
  min-height: 300px;
  text-align: center;

  &:hover {
    border-color: #0052d9;
    background: #f0f7ff;
  }

  .upload-text {
    margin-top: 16px;
    color: #333;
    font-size: 16px;
  }

  .upload-actions {
    margin: 16px 0;
  }

  .upload-tip {
    margin-top: 8px;
    color: #999;
    font-size: 14px;
  }
}

.right-config {
  padding: 10px;
}

.title-right {
  font-size: 20px;
  color: #808080;
  border-bottom: 1px solid #bfbfbf;
  margin-bottom: 22px;
}

.checkbox-big {
  width: 100%;
  display: flex;
  justify-content: space-between;
}

.checkbox-li {
  width: 23%;
  font-size: 16px;

  .divider {
    margin: 6px 0;
    border-bottom: 1px solid #808080;
  }

  .t-checkbox-group {
    display: flex;
    flex-direction: column;

    .t-checkbox {
      margin-top: 10px;
    }
  }
}

.export-section {
  text-align: right;
  margin-bottom: 20px;
}

.export-btn {
  margin-right: 44px;
}

.result-table {
  margin-top: 20px;
}

.action-section {
  text-align: right;
  width: 100%;
  margin-top: 20px;
}

.action-btn {
  margin-right: 60px;
}

.notice-section {
  text-align: center;
  margin-top: 30px;
  padding-top: 20px;
  border-top: 1px solid #eee;
}

.download-notice {
  font-size: 14px;
  color: #666;
  display: inline-block;
}

.notice-icon {
  color: #ff9800;
  margin-right: 5px;
}

.task-table {
  margin-top: 16px;
}

// 响应式设计
@media (max-width: 1200px) {
  .center {
    padding: 20px;
  }

  .checkbox-big {
    grid-template-columns: 1fr;
  }

  .header-section {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }

  .task-list-btn {
    margin-right: 0;
  }
}

@media (max-width: 768px) {
  .data-classification-container {
    padding: 16px;
  }

  .center {
    padding: 16px;
  }

  .left-config {
    border-right: none;
    border-bottom: 2px solid #808080;
    padding-right: 0;
    padding-bottom: 20px;
    margin-bottom: 20px;
  }

  .upload-trigger {
    min-height: 150px;
    padding: 24px 16px;
  }

  .action-section {
    text-align: center;
  }

  .action-btn {
    margin-right: 0;
  }
}
</style>
